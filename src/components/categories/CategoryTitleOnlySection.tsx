import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';
import { TriangleAlert } from 'lucide-react';

interface CategoryTitleOnlySectionProps {
  articles: Article[];
  categoryInfo: Category;
}

export default function CategoryTitleOnlySection({
  articles,
  categoryInfo,
}: CategoryTitleOnlySectionProps) {
  if (articles.length === 0) {
    return (
      <div className="text-center py-6">
        <div className="text-muted-foreground">
          <TriangleAlert className="size-4 mx-auto mb-3" />
          <p className="text-xs mb-1"><PERSON><PERSON></p>
          <p className="text-xs">verfügbar</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2 md:space-y-3">
      {articles.map((article, index) => (
        <NewsCard
          key={article.id}
          article={article}
          variant="title-only"
          showDescription={true}
          locale="de"
          hideCategory={true}
          className={`${index === 0 ? 'border-t-2 border-blue-200 dark:border-blue-800 pt-2' : ''} pb-2 ${index < articles.length - 1 ? 'border-b border-gray-100 dark:border-gray-800' : ''}`}
        />
      ))}

      {articles.length > 0 && (
        <div className="mt-4 pt-2 border-t border-gray-100 dark:border-gray-800">
          <p className="text-xs text-muted-foreground text-center">
            {articles.length} Artikel
          </p>
        </div>
      )}
    </div>
  );
}
