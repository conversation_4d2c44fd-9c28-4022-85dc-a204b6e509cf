import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';

interface CategoryFeaturedSectionProps {
  articles: Article[];
  categoryInfo: Category;
}

export default function CategoryFeaturedSection({
  articles,
  categoryInfo,
}: CategoryFeaturedSectionProps) {
  if (articles.length === 0) return null;

  return (
    <section
      className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6"
      aria-labelledby="featured-grid-heading"
    >
      <h3 id="featured-grid-heading" className="sr-only">
        Featured {categoryInfo.title} Articles
      </h3>

      {articles.map((article, index) => (
        <NewsCard
          key={article.id}
          article={article}
          variant="default"
          showDescription={true}
          locale="de"
          hideCategory={true}
          priority={index < 2} // Priority for first 2 articles
          className="shadow-sm"
        />
      ))}
    </section>
  );
}
