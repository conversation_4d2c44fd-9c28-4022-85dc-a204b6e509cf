import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';

interface CategoryHeroSectionProps {
  article: Article;
  categoryInfo: Category;
}

export default function CategoryHeroSection({
  article,
  categoryInfo,
}: CategoryHeroSectionProps) {
  return (
    <section className="mb-6 md:mb-8">
      <NewsCard
        article={article}
        variant="default"
        showDescription={true}
        priority={true}
        locale="de"
        hideCategory={true}
        className="shadow-sm border-2 border-gray-100 dark:border-gray-800"
      />

      {article.pinned && (
        <div className="mt-3 flex items-center gap-2 text-xs text-muted-foreground">
          <span className="size-2 bg-blue-500 rounded-full animate-pulse" />
          <span>Wichtiger {categoryInfo.title}-Artikel</span>
        </div>
      )}
    </section>
  );
}
