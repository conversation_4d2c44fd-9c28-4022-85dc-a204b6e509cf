import {
  getCachedCategoryArticles,
  organizeCategoryLayout,
} from '@/lib/cache/categories';
import CategoryHeroSection from './CategoryHeroSection';
import CategoryFeaturedSection from './CategoryFeaturedSection';
import CategorySidebar from './CategorySidebar';
import CategoryAdditionalSection from './CategoryAdditionalSection';
import CategoryEmptyState from './CategoryEmptyState';
import CategoryVerticalSection from './CategoryVerticalSection';
import CategoryTitleOnlySection from './CategoryTitleOnlySection';
import { TriangleAlert } from 'lucide-react';

interface CategoryPageLayoutProps {
  categorySlug: string;
}

export default async function CategoryPageLayout({
  categorySlug,
}: CategoryPageLayoutProps) {
  try {
    const getCachedData = getCachedCategoryArticles(categorySlug);
    const categoryData = await getCachedData();

    if (!categoryData) {
      // Create a minimal category object for the empty state
      const emptyCategory = {
        id: 0,
        title: 'Category Not Found',
        english: 'Category Not Found',
        slug: categorySlug,
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      } as const;
      return <CategoryEmptyState category={emptyCategory} />;
    }

    const layout = organizeCategoryLayout(categoryData);

    if (layout.contentStrategy === 'tier-empty') {
      return <CategoryEmptyState category={categoryData.categoryInfo} />;
    }

    return (
      <div className="max-w-[1440px] mx-auto px-4 py-6 sm:py-8 md:py-10 lg:py-12">
        {/* 4-Column Grid Layout */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">
          {/* Columns 1-2: Hero + Featured Articles */}
          <section
            className="sm:col-span-1 lg:col-span-2 xl:col-span-2 sm:border-r-2 lg:border-r-2 xl:border-r-2 sm:border-gray-50 lg:border-gray-50 xl:border-gray-50 sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-1"
            aria-labelledby="main-content-heading"
          >
            <h2 id="main-content-heading" className="sr-only">
              Hauptartikel {categoryData.categoryInfo.title}
            </h2>

            {/* Hero Article */}
            {layout.hero && (
              <div className="mb-4 md:mb-6">
                <CategoryHeroSection
                  article={layout.hero}
                  categoryInfo={categoryData.categoryInfo}
                />
              </div>
            )}

            {/* Featured Articles - 2 articles in 1x2 grid below hero */}
            {layout.featured.length > 0 && (
              <CategoryFeaturedSection
                articles={layout.featured}
                categoryInfo={categoryData.categoryInfo}
              />
            )}
          </section>

          {/* Column 3: Vertical List */}
          <aside
            className="sm:border-r-2 lg:border-r-2 xl:border-r-2 sm:border-gray-50 lg:border-gray-50 xl:border-gray-50 sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-2 lg:order-2"
            aria-labelledby="vertical-list-heading"
          >
            <h2 id="vertical-list-heading" className="sr-only">
              Weitere {categoryData.categoryInfo.title} Artikel
            </h2>
            <CategoryVerticalSection
              articles={layout.sidebar.slice(0, 3)}
              categoryInfo={categoryData.categoryInfo}
            />
          </aside>

          {/* Column 4: Title-Only Posts */}
          <aside
            className="xl:pl-4 order-3 hidden xl:block"
            aria-labelledby="title-only-heading"
          >
            <h2 id="title-only-heading" className="sr-only">
              {categoryData.categoryInfo.title} Schlagzeilen
            </h2>
            <CategoryTitleOnlySection
              articles={layout.sidebar.slice(3)}
              categoryInfo={categoryData.categoryInfo}
            />
          </aside>
        </div>

        {/* Additional Content (Bottom Section - spans full width) */}
        {layout.additional.length > 0 && (
          <div className="mt-8 md:mt-10 lg:mt-12 pt-6 md:pt-8 border-t">
            <CategoryAdditionalSection
              articles={layout.additional}
              categoryInfo={categoryData.categoryInfo}
            />
          </div>
        )}
      </div>
    );
  } catch (error) {
    console.error('Error loading category layout:', error);

    return (
      <div className="max-w-[1440px] mx-auto px-4 py-12">
        <div className="text-center">
          <div className="text-muted-foreground">
            <TriangleAlert className="size-4 mx-auto mb-3" />
            <p className="text-sm mb-2">Fehler beim Laden der Kategorie</p>
            <p className="text-xs">Schauen Sie später noch einmal vorbei</p>
          </div>
        </div>
      </div>
    );
  }
}
