import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';

interface CategoryAdditionalSectionProps {
  articles: Article[];
  categoryInfo: Category;
}

export default function CategoryAdditionalSection({
  articles,
  categoryInfo,
}: CategoryAdditionalSectionProps) {
  if (articles.length === 0) return null;

  return (
    <section
      className="border-t pt-6 md:pt-8 space-y-4 md:space-y-6"
      aria-labelledby="additional-content-heading"
    >
      <div className="flex items-center justify-between">
        <h3
          id="additional-content-heading"
          className="font-serif text-lg/6 md:text-xl/7 font-normal text-foreground"
        >
          Weitere {categoryInfo.title}-Artikel
        </h3>
        <span className="font-sans text-xs text-muted-foreground">
          {articles.length} Artikel
        </span>
      </div>

      {/* Horizontal grid for additional articles */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        {articles.map((article, index) => (
          <NewsCard
            key={article.id}
            article={article}
            variant="title-only"
            showDescription={true}
            locale="de"
            hideCategory={true}
            priority={index < 3} // Priority for first 3 articles
          />
        ))}
      </div>

      {articles.length >= 12 && (
        <div className="text-center pt-4">
          <p className="font-sans text-sm text-muted-foreground">
            Weitere Artikel in der Kategorie {categoryInfo.title} verfügbar
          </p>
        </div>
      )}
    </section>
  );
}
