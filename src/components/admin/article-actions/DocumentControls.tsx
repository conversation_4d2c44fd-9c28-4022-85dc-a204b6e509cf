/**
 * Article Document Controls Component
 *
 * Provides translation functionality for candidate articles with proper UI updates.
 *
 * Key Features:
 * - Translates enhanced English content to German using OpenAI
 * - Updates form fields programmatically using PayloadCMS patterns
 * - Forces page refresh to ensure UI reflects changes immediately
 * - Provides visual feedback during translation process
 * - <PERSON>les re-translation scenarios
 *
 * Solution for UI Update Issue:
 * - Uses dispatchFields() to update form state
 * - Implements router.refresh() to force page re-render
 * - Provides immediate visual feedback with button state changes
 * - Includes fallback option for window.location.reload() if needed
 *
 * <AUTHOR> Blick Development Team
 * @updated 2025-01-16 - Fixed UI refresh issue after translation
 */
'use client';

import React, { useState, useCallback } from 'react';
import {
  useDocumentInfo,
  useAllFormFields,
  useFormFields,
} from '@payloadcms/ui';
import { useRouter } from 'next/navigation';
import { lexicalToText } from '../../../lib/utils/lexical-text';

// Temporary toast implementation - will be replaced with proper PayloadCMS notifications
interface ToastOptions {
  description?: string;
  duration?: number;
}

const useToast = () => {
  return {
    toast: {
      error: (message: string, options?: ToastOptions) =>
        console.error('Toast Error:', message, options),
      success: (message: string, options?: ToastOptions) =>
        console.log('Toast Success:', message, options),
    },
  };
};

// Helper to reduce fields to values - simplified and safe approach
const reduceFieldsToValues = (fields: any) => {
  const values: any = {};

  // Simple approach: just extract direct field values and handle known tab structure
  Object.keys(fields).forEach(key => {
    const field = fields[key];
    if (field && typeof field === 'object' && 'value' in field) {
      values[key] = field.value;
    }
  });

  return values;
};

export const ArticleDocumentControls = () => {
  const docInfo = useDocumentInfo();
  const { id } = docInfo;
  const [fields, dispatchFields] = useAllFormFields();

  const { toast } = useToast();
  const router = useRouter();
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationJustCompleted, setTranslationJustCompleted] =
    useState(false);

  // Enhancement state management - follows same pattern as translation
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementJustCompleted, setEnhancementJustCompleted] =
    useState(false);

  // Get CURRENT form field values using PayloadCMS hooks
  const title =
    useFormFields(([fields]) => fields.title?.value as string) || '';
  const articleType = useFormFields(
    ([fields]) => fields.articleType?.value as string
  );
  const workflowStage = useFormFields(
    ([fields]) => fields.workflowStage?.value as string
  );
  const enhancedTitle =
    useFormFields(
      ([fields]) => fields['englishTab.enhancedTitle']?.value as string
    ) || '';
  const enhancedSummary =
    useFormFields(
      ([fields]) => fields['englishTab.enhancedSummary']?.value as string
    ) || '';
  const enhancedContent = useFormFields(
    ([fields]) => fields['englishTab.enhancedContent']?.value
  );
  const hasBeenEnhanced = useFormFields(
    ([fields]) => fields.hasBeenEnhanced?.value as boolean
  );
  const germanTitle = useFormFields(
    ([fields]) => fields['germanTab.germanTitle']?.value as string
  );
  const germanContent = useFormFields(
    ([fields]) => fields['germanTab.germanContent']?.value
  );

  // Convert fields to data for compatibility with existing logic
  const data = reduceFieldsToValues(fields);
  const hasGermanTranslation = germanTitle && germanContent;

  // SIMPLE validation using current form values
  const validateForEnhancement = () => {
    return {
      title: title.length >= 20,
      hasContent: enhancedTitle.length >= 10 && enhancedSummary.length >= 20,
    };
  };

  // Translate button: Check enhanced content (after enhancement)
  const validateForTranslation = () => {
    let contentText = '';
    if (enhancedContent && typeof enhancedContent === 'object') {
      contentText = lexicalToText(enhancedContent);
    } else if (typeof enhancedContent === 'string') {
      contentText = enhancedContent;
    }

    return {
      enhancedTitle: enhancedTitle.length >= 20,
      enhancedSummary: enhancedSummary.length >= 20,
      enhancedContent: contentText.length >= 20,
    };
  };

  const enhanceValidation = validateForEnhancement();
  const translateValidation = validateForTranslation();

  const enhanceFieldsValid = Object.values(enhanceValidation).every(v => v);
  const translateFieldsValid = Object.values(translateValidation).every(v => v);

  // Simple detection: Has been enhanced flag = already enhanced by AI (same pattern as translation)
  const isEnhanced = !!(hasBeenEnhanced || enhancementJustCompleted);

  // Clear validation messages for specific fields
  const getValidationMessage = useCallback(() => {
    if (!id) return 'Please save the article first';
    if (!enhanceValidation.title) return 'Title needs at least 20 characters';
    if (!enhanceValidation.hasContent)
      return 'Article needs basic content (title, summary, or content) to be enhanced.';
    if (!translateValidation.enhancedTitle)
      return 'Enhanced English Title needs at least 20 characters';
    if (!translateValidation.enhancedSummary)
      return 'Enhanced English Summary needs at least 20 characters';
    if (!translateValidation.enhancedContent)
      return 'Enhanced English Content needs at least 20 characters';
    return null;
  }, [id, enhanceValidation, translateValidation]);

  // Translation button logic:
  // For curated articles: Can translate when enhanced fields meet validation (20+ chars each)
  // For generated articles: Must be AI-enhanced first
  const canTranslate =
    articleType === 'curated'
      ? // Curated: Use enhanced field validation (20+ chars each field)
        !!id && // Must be saved first
        translateFieldsValid && // All enhanced fields have 20+ characters
        [
          'curated-draft',
          'candidate-article',
          'translated',
          'ready-for-review',
          'published',
        ].includes(workflowStage)
      : // Generated: Must be AI-enhanced first
        isEnhanced &&
        [
          'curated-draft',
          'candidate-article',
          'translated',
          'ready-for-review',
          'published',
        ].includes(workflowStage);

  // Enhancement button logic: Only show for curated articles
  const canEnhanceContent =
    !!id && // Must be saved first
    articleType === 'curated' &&
    ['curated-draft', 'candidate-article'].includes(workflowStage) && // Allow re-enhancement like re-translation
    enhanceFieldsValid &&
    !isEnhancing &&
    !isTranslating; // Prevent simultaneous operations

  // Button visibility logic following PayloadCMS best practices
  const showEnhanceButton = articleType === 'curated' && !!id; // Only for curated articles
  const showTranslationButton = !!id; // Show for all saved articles

  // Debug logging to diagnose button visibility issues
  React.useEffect(() => {
    console.log('🔍 DocumentControls Debug:', {
      id,
      articleType,
      workflowStage,
      isEnhanced,
      enhanceFieldsValid,
      translateFieldsValid,
      enhanceValidation,
      translateValidation,
      showEnhanceButton,
      showTranslationButton,
      rawData: data,
      englishTabData: data?.englishTab,
      // Debug field access attempts - now using correct nested object notation
      fieldAccessDebug: {
        title: title,
        enhancedTitle_nestedObject: enhancedTitle,
        enhancedSummary_nestedObject: enhancedSummary,
        enhancedContent_nestedObject: enhancedContent,
        enhancedContent_textLength: enhancedContent
          ? lexicalToText(enhancedContent).length
          : 0,
        // NEW: Original content fields for enhancement validation
        originalTitle: data?.sourcesTab?.originalTitle,
        originalSummary: data?.sourcesTab?.originalSummary,
        originalContent_length: data?.sourcesTab?.originalContent
          ? lexicalToText(data.sourcesTab.originalContent).length
          : 0,
        // DEBUGGING: Show detailed validation step by step
        validationBreakdown: {
          titleCheck: title.length >= 20,
          enhancedTitleLength: enhancedTitle.length,
          enhancedSummaryLength: enhancedSummary.length,
          condition1:
            enhancedTitle.length >= 10 && enhancedSummary.length >= 20,
          condition2:
            enhancedTitle.length >= 10 &&
            (enhancedContent ? lexicalToText(enhancedContent).length : 0) >= 50,
        },
      },
    });
  }, [
    id,
    articleType,
    workflowStage,
    isEnhanced,
    enhanceFieldsValid,
    translateFieldsValid,
    data,
    title,
    enhancedTitle,
    enhancedSummary,
    enhancedContent,
  ]);

  // Translation handler - for both curated and generated articles
  const handleTranslateToGerman = useCallback(async () => {
    if (!canTranslate) {
      const validationMessage = getValidationMessage();
      toast.error(validationMessage || 'Cannot translate article at this time');
      return;
    }

    setIsTranslating(true);

    try {
      const response = await fetch('/api/articles/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          articleId: id,
          // No form data - API will work with saved database content only
        }),
      });

      const result = await response.json();

      if (result.success && result.translatedContent) {
        // Show success feedback to user
        toast.success('German translation completed successfully!', {
          description: hasGermanTranslation
            ? 'Article has been re-translated to German.'
            : 'Article has been translated to German.',
          duration: 3000,
        });

        // Update form fields using PayloadCMS native patterns
        // The API response contains the translated content
        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanTitle',
          value: result.translatedContent.germanTitle,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanContent',
          value: result.translatedContent.germanContent,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'germanTab.germanSummary',
          value: result.translatedContent.germanSummary,
        });

        // Update additional fields if they exist in the response
        if (result.translatedContent.germanKeyInsights) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanKeyInsights',
            value: result.translatedContent.germanKeyInsights,
          });
        }

        if (result.translatedContent.germanKeywords) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanKeywords',
            value: result.translatedContent.germanKeywords,
          });
        }

        dispatchFields({
          type: 'UPDATE',
          path: 'hasGermanTranslation',
          value: true,
        });

        // Update workflow stage to 'translated'
        dispatchFields({
          type: 'UPDATE',
          path: 'workflowStage',
          value: 'translated',
        });

        // Set immediate visual feedback
        setTranslationJustCompleted(true);

        // Force form re-render using PayloadCMS best practices
        setTimeout(() => {
          router.refresh();
        }, 1500);

        // Reset the completion flag after refresh
        setTimeout(() => {
          setTranslationJustCompleted(false);
        }, 2000);
      } else {
        toast.error('Translation failed', {
          description:
            result.error || 'An unknown error occurred during translation',
        });
      }
    } catch (error) {
      toast.error('Translation failed', {
        description: 'Failed to communicate with the translation service',
      });
    } finally {
      setIsTranslating(false);
    }
  }, [
    canTranslate,
    id,
    hasGermanTranslation,
    getValidationMessage,
    dispatchFields,
    toast,
    router,
  ]);

  // Enhancement handler - for curated articles
  const handleEnhance = useCallback(async () => {
    if (!canEnhanceContent) {
      const validationMessage = getValidationMessage();
      toast.error(validationMessage || 'Cannot enhance article at this time');
      return;
    }

    setIsEnhancing(true);

    try {
      const response = await fetch('/api/articles/enhance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId: id }),
      });

      const result = await response.json();

      if (result.success && result.enhancedContent) {
        // Show success feedback to user (same as translation)
        toast.success('Content enhanced successfully!', {
          description: isEnhanced
            ? 'Article has been re-enhanced.'
            : 'Article has been enhanced with AI.',
          duration: 3000,
        });

        // Update form fields using PayloadCMS native patterns (same as translation)
        // The API response contains the enhanced content
        dispatchFields({
          type: 'UPDATE',
          path: 'englishTab.enhancedTitle',
          value: result.enhancedContent.enhancedTitle,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'englishTab.enhancedContent',
          value: result.enhancedContent.enhancedContent,
        });

        dispatchFields({
          type: 'UPDATE',
          path: 'englishTab.enhancedSummary',
          value: result.enhancedContent.summary,
        });

        // Update additional fields if they exist in the response
        if (result.enhancedContent.keyInsights) {
          dispatchFields({
            type: 'UPDATE',
            path: 'englishTab.enhancedKeyInsights',
            value: result.enhancedContent.keyInsights,
          });
        }

        if (result.enhancedContent.keywords) {
          dispatchFields({
            type: 'UPDATE',
            path: 'englishTab.keywords',
            value: result.enhancedContent.keywords,
          });
        }

        // Set enhancement flag (critical for button state changes)
        dispatchFields({
          type: 'UPDATE',
          path: 'hasBeenEnhanced',
          value: true,
        });

        // Update workflow stage to 'candidate-article'
        dispatchFields({
          type: 'UPDATE',
          path: 'workflowStage',
          value: 'candidate-article',
        });

        // Set immediate visual feedback (same as translation)
        setEnhancementJustCompleted(true);

        // Force form re-render to load updated data from database
        setTimeout(() => {
          router.refresh();
        }, 1000);

        // Reset the completion flag after refresh
        setTimeout(() => {
          setEnhancementJustCompleted(false);
        }, 2000);
      } else {
        toast.error('Enhancement failed', {
          description: result.error || 'Unknown error during enhancement',
        });
      }
    } catch (error) {
      toast.error('Enhancement failed', {
        description: 'Failed to communicate with enhancement service',
      });
    } finally {
      setIsEnhancing(false);
    }
  }, [
    canEnhanceContent,
    id,
    getValidationMessage,
    dispatchFields,
    toast,
    router,
    isEnhanced,
  ]);

  // Show for both generated and curated articles with appropriate workflow stages
  const validArticleTypes = ['generated', 'curated'];
  const validWorkflowStages = [
    'curated-draft', // New stage for curated articles
    'candidate-article',
    'translated',
    'ready-for-review',
    'published',
  ];

  // Debug early return conditions
  console.log('🔍 Early return check:', {
    articleType,
    workflowStage,
    validArticleTypes,
    validWorkflowStages,
    articleTypeValid: validArticleTypes.includes(articleType),
    workflowStageValid: validWorkflowStages.includes(workflowStage),
  });

  if (
    !validArticleTypes.includes(articleType) ||
    !validWorkflowStages.includes(workflowStage)
  ) {
    console.log('🚫 Early return triggered - component will not render');
    return null;
  }

  // Button disabled states using proper business logic
  const isTranslationDisabled =
    !canTranslate ||
    isTranslating ||
    isEnhancing ||
    translationJustCompleted ||
    enhancementJustCompleted;

  const isEnhancementDisabled =
    !canEnhanceContent ||
    isEnhancing ||
    isTranslating ||
    enhancementJustCompleted ||
    translationJustCompleted;

  // Enhancement button helper functions
  const getEnhanceButtonText = () => {
    if (isEnhancing) return 'Enhancing Content...';
    if (enhancementJustCompleted) return 'Enhancement Complete! Refreshing...';
    if (isEnhanced) return 'Re-enhance Content';
    return 'Enhance Content';
  };

  const getEnhanceButtonColor = () => {
    if (isEnhancing) return '#6B7280'; // Gray for loading
    if (enhancementJustCompleted) return '#10B981'; // Bright green for success
    if (isEnhanced) return '#8B5CF6'; // Purple for re-enhancement
    return '#3B82F6'; // Blue for first enhancement
  };

  const getTranslationButtonText = () => {
    if (isTranslating) return 'Translating to German...';
    if (translationJustCompleted) return 'Translation Complete! Refreshing...';
    if (hasGermanTranslation) return 'Re-Translate to German';
    return 'Translate to German';
  };

  const getTranslationButtonColor = () => {
    if (isTranslating) return '#6B7280'; // Gray for loading
    if (translationJustCompleted) return '#10B981'; // Bright green for success
    if (hasGermanTranslation) return '#059669'; // Green for re-translation
    return '#2563EB'; // Blue for first translation
  };

  return (
    <div
      className="flex gap-4 items-center mb-4"
      style={{
        marginBottom: 'var(--base, 1rem)',
        gap: 'var(--base-half, 0.5rem)', // Ensure proper spacing between buttons
      }}
    >
      {/* Enhancement Button - Only show for curated articles in draft stage */}
      {showEnhanceButton && (
        <button
          onClick={handleEnhance}
          disabled={isEnhancementDisabled}
          title={
            !id
              ? 'Please save the article first before enhancement'
              : !canEnhanceContent
                ? getValidationMessage() ||
                  'Please complete all required fields (20+ characters each)'
                : undefined
          }
          className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-colors"
          style={{
            backgroundColor: getEnhanceButtonColor(),
            color: 'white',
            border: 'none',
            cursor: isEnhancementDisabled ? 'not-allowed' : 'pointer',
            opacity: isEnhancementDisabled ? 0.6 : 1,
            fontSize: 'var(--font-size-sm, 0.875rem)',
            padding:
              'var(--base-quarter, 0.5rem) var(--base-three-quarters, 0.75rem)',
            borderRadius: 'var(--border-radius-m, 0.375rem)',
            minHeight: '32px',
          }}
        >
          {isEnhancing && (
            <div
              style={{
                width: '14px',
                height: '14px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                flexShrink: 0,
              }}
            />
          )}
          {enhancementJustCompleted && (
            <div
              style={{
                width: '14px',
                height: '14px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              ✓
            </div>
          )}
          <span style={{ lineHeight: '1' }}>{getEnhanceButtonText()}</span>
        </button>
      )}

      {/* Translation Button - Only show when conditions are met */}
      {showTranslationButton && (
        <button
          onClick={handleTranslateToGerman}
          disabled={isTranslationDisabled}
          title={
            !id
              ? 'Please save the article first before translation'
              : !canTranslate
                ? getValidationMessage() ||
                  'Please complete all required fields (20+ characters each)'
                : undefined
          }
          className="inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md border transition-colors"
          style={{
            backgroundColor: getTranslationButtonColor(),
            color: 'white',
            border: 'none',
            cursor: isTranslationDisabled ? 'not-allowed' : 'pointer',
            opacity: isTranslationDisabled ? 0.6 : 1,
            fontSize: 'var(--font-size-sm, 0.875rem)',
            padding:
              'var(--base-quarter, 0.5rem) var(--base-three-quarters, 0.75rem)',
            borderRadius: 'var(--border-radius-m, 0.375rem)',
            minHeight: '32px',
          }}
        >
          {isTranslating && (
            <div
              style={{
                width: '14px',
                height: '14px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                flexShrink: 0,
              }}
            />
          )}
          {translationJustCompleted && (
            <div
              style={{
                width: '14px',
                height: '14px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              ✓
            </div>
          )}
          <span style={{ lineHeight: '1' }}>{getTranslationButtonText()}</span>
        </button>
      )}

      <style jsx>{`
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export default ArticleDocumentControls;
