import { unstable_cache } from 'next/cache';
import { getPayload } from 'payload';
import config from '@/payload.config';
import type { Article, Category } from '@/payload-types';
import {
  CACHE_DURATIONS,
  CACHE_TAGS,
  FIELD_SETS,
  generateCacheKey,
} from './constants';

// Type definitions for category-specific content
export interface CategoryTierData {
  tier1: Article[];
  tier2: Article[];
  tier3: Article[];
  totalArticles: number;
  categoryInfo: Category;
}

export interface CategoryPageLayout {
  hero: Article | null;
  featured: Article[]; // 4 articles for grid below hero
  sidebar: Article[]; // Mixed tier content for right sidebar
  additional: Article[]; // Bottom section content
  fallbackUsed: boolean;
  contentStrategy: 'tier-1-rich' | 'tier-1-sparse' | 'tier-empty';
}

// Enhanced category fetching with tier-based queries
export const getCachedCategoryArticles = (categorySlug: string) =>
  unstable_cache(
    async (): Promise<CategoryTierData | null> => {
      const payload = await getPayload({ config });

      // Fetch category info first
      const categoryResult = await payload.find({
        collection: 'categories',
        where: { slug: { equals: categorySlug } },
        limit: 1,
        depth: 1,
      });

      if (categoryResult.docs.length === 0) {
        // ✅ FIX: Return null instead of throwing error to allow proper 404 page handling
        return null;
      }

      const category = categoryResult.docs[0];

      // ✅ PHASE 2: Smart field selection to prevent 2MB cache limit
      const [tier1, tier2, tier3] = await Promise.all([
        // Tier 1 - Use HERO_FEATURED for rich display (these might be heroes)
        payload.find({
          collection: 'articles',
          where: {
            and: [
              { categories: { in: [category.id] } },
              { placement: { equals: 'tier-1' } },
              { _status: { equals: 'published' } },
            ],
          },
          sort: ['-pinned', '-publishedAt'],
          limit: 8,
          depth: 1,
          select: FIELD_SETS.HERO_FEATURED, // ✅ Smart field selection
        }),
        // Tier 2 - Use LIST_MINIMAL for efficient list display
        payload.find({
          collection: 'articles',
          where: {
            and: [
              { categories: { in: [category.id] } },
              { placement: { equals: 'tier-2' } },
              { _status: { equals: 'published' } },
            ],
          },
          sort: ['-publishedAt'],
          limit: 8,
          depth: 1,
          select: FIELD_SETS.LIST_MINIMAL, // ✅ Smart field selection
        }),
        // Tier 3 - Use LIST_MINIMAL for efficient list display
        payload.find({
          collection: 'articles',
          where: {
            and: [
              { categories: { in: [category.id] } },
              { placement: { equals: 'tier-3' } },
              { _status: { equals: 'published' } },
            ],
          },
          sort: ['-publishedAt'],
          limit: 10,
          depth: 1,
          select: FIELD_SETS.LIST_MINIMAL, // ✅ Smart field selection
        }),
      ]);

      return {
        tier1: tier1.docs,
        tier2: tier2.docs,
        tier3: tier3.docs,
        totalArticles: tier1.totalDocs + tier2.totalDocs + tier3.totalDocs,
        categoryInfo: category,
      };
    },
    [`category-articles-${categorySlug}`],
    {
      revalidate: CACHE_DURATIONS.DEFAULT, // ✅ UNIFIED: Use consistent cache duration
      tags: [
        CACHE_TAGS.ARTICLES,
        CACHE_TAGS.CATEGORIES,
        `category-${categorySlug}`,
      ], // ✅ UNIFIED: Use consistent tags
    }
  );

// Intelligent content distribution logic
export const organizeCategoryLayout = (
  data: CategoryTierData
): CategoryPageLayout => {
  const { tier1, tier2, tier3, totalArticles } = data;

  // Strategy 1: Rich Tier 1 content (6+ Tier 1 articles)
  if (tier1.length >= 6) {
    return {
      hero: tier1[0],
      featured: tier1.slice(1, 5), // Next 4 articles
      sidebar: [...tier1.slice(5), ...tier2.slice(0, 6)], // Remaining T1 + T2
      additional: tier3.slice(0, 12), // Tier 3 for bottom
      fallbackUsed: false,
      contentStrategy: 'tier-1-rich',
    };
  }

  // Strategy 2: Sparse Tier 1 content (3-5 Tier 1 articles)
  if (tier1.length >= 3) {
    return {
      hero: tier1[0],
      featured: tier1.slice(1, Math.min(5, tier1.length)),
      sidebar: [
        ...tier1.slice(Math.min(5, tier1.length)),
        ...tier2.slice(0, 8),
      ],
      additional: [...tier2.slice(8), ...tier3.slice(0, 10)],
      fallbackUsed: false,
      contentStrategy: 'tier-1-sparse',
    };
  }

  // Strategy 3: Very limited content (< 3 Tier 1 articles)
  // Promote Tier 2 to featured positions
  const allContent = [...tier1, ...tier2, ...tier3];

  if (allContent.length === 0) {
    return {
      hero: null,
      featured: [],
      sidebar: [],
      additional: [],
      fallbackUsed: false,
      contentStrategy: 'tier-empty',
    };
  }

  return {
    hero: allContent[0] || null,
    featured: allContent.slice(1, 5),
    sidebar: allContent.slice(5, 13),
    additional: allContent.slice(13, 25),
    fallbackUsed: true,
    contentStrategy: 'tier-1-sparse',
  };
};

// Fetch all category slugs for static generation
export const getCachedCategorySlugs = unstable_cache(
  async (): Promise<{ slug: string }[]> => {
    const payload = await getPayload({ config });

    const result = await payload.find({
      collection: 'categories',
      limit: 100, // Reasonable limit for categories
      depth: 0, // Only need slug field
    });

    return result.docs
      .filter(category => category.slug)
      .map(category => ({ slug: category.slug! }));
  },
  ['category-slugs'],
  {
    revalidate: CACHE_DURATIONS.STATIC_CONTENT, // ✅ UNIFIED: Use appropriate cache duration for static content
    tags: [CACHE_TAGS.CATEGORIES], // ✅ UNIFIED: Use consistent tag naming
  }
);

// Get category info by slug
export const getCachedCategoryBySlug = (categorySlug: string) =>
  unstable_cache(
    async (): Promise<Category | null> => {
      const payload = await getPayload({ config });

      const result = await payload.find({
        collection: 'categories',
        where: { slug: { equals: categorySlug } },
        limit: 1,
        depth: 1,
      });

      return result.docs[0] || null;
    },
    [`category-info-${categorySlug}`],
    {
      revalidate: CACHE_DURATIONS.STATIC_CONTENT, // ✅ UNIFIED: Use consistent cache duration for category info
      tags: [CACHE_TAGS.CATEGORIES, `category-${categorySlug}`], // ✅ UNIFIED: Use consistent tag naming
    }
  );

// Fetch all categories with full info for navigation and 404 pages
export const getCachedAllCategories = unstable_cache(
  async (): Promise<Category[]> => {
    const payload = await getPayload({ config });

    const result = await payload.find({
      collection: 'categories',
      limit: 100, // Reasonable limit for categories
      depth: 0, // Only need basic fields
    });

    return result.docs.filter(category => category.slug);
  },
  ['all-categories'],
  {
    revalidate: CACHE_DURATIONS.STATIC_CONTENT, // ✅ UNIFIED: Use appropriate cache duration for static content
    tags: [CACHE_TAGS.CATEGORIES], // ✅ UNIFIED: Use consistent tag naming
  }
);
