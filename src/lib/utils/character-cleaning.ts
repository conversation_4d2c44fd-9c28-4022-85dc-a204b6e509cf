/**
 * Character Cleaning Utilities for Börsen Blick
 *
 * Provides functions to clean unwanted characters from AI-generated content
 * to ensure SEO-friendly, professional English output.
 */

/**
 * Clean title text for SEO optimization
 * More aggressive cleaning for titles since they're critical for SEO
 */
export function cleanTitle(title: string): string {
  if (!title) return '';

  let cleaned = title;

  // Remove asterisks and other markdown formatting
  cleaned = cleaned.replace(/\*+/g, '');

  // Remove emojis (comprehensive Unicode ranges)
  cleaned = cleaned.replace(/[\u{1F600}-\u{1F64F}]/gu, ''); // Emoticons
  cleaned = cleaned.replace(/[\u{1F300}-\u{1F5FF}]/gu, ''); // Misc Symbols
  cleaned = cleaned.replace(/[\u{1F680}-\u{1F6FF}]/gu, ''); // Transport
  cleaned = cleaned.replace(/[\u{1F1E0}-\u{1F1FF}]/gu, ''); // Flags
  cleaned = cleaned.replace(/[\u{2600}-\u{26FF}]/gu, ''); // Misc symbols
  cleaned = cleaned.replace(/[\u{2700}-\u{27BF}]/gu, ''); // Dingbats
  cleaned = cleaned.replace(/[\u{1F900}-\u{1F9FF}]/gu, ''); // Supplemental Symbols
  cleaned = cleaned.replace(/[\u{1FA70}-\u{1FAFF}]/gu, ''); // Extended symbols

  // Remove other special Unicode characters that hurt SEO
  cleaned = cleaned.replace(/[\u{2000}-\u{206F}]/gu, ' '); // General punctuation to space
  cleaned = cleaned.replace(/[\u{2070}-\u{209F}]/gu, ''); // Superscripts/subscripts
  cleaned = cleaned.replace(/[\u{20A0}-\u{20CF}]/gu, ''); // Currency symbols (except basic ones)
  cleaned = cleaned.replace(/[\u{2100}-\u{214F}]/gu, ''); // Letterlike symbols
  cleaned = cleaned.replace(/[\u{2190}-\u{21FF}]/gu, ''); // Arrows
  cleaned = cleaned.replace(/[\u{2200}-\u{22FF}]/gu, ''); // Mathematical operators

  // Remove foreign characters that don't belong in English
  // Keep basic Latin, numbers, and essential punctuation
  cleaned = cleaned.replace(/[^\u0020-\u007E\u00A0-\u00FF]/g, '');

  // Clean up multiple spaces and trim
  cleaned = cleaned.replace(/\s+/g, ' ').trim();

  // Remove leading/trailing punctuation except periods
  cleaned = cleaned.replace(/^[^\w\d]+|[^\w\d.]+$/g, '');

  return cleaned;
}

/**
 * Clean content text while preserving some formatting
 * Less aggressive than title cleaning to maintain readability
 */
export function cleanContent(content: string): string {
  if (!content) return '';

  let cleaned = content;

  // Remove emojis (same ranges as title)
  cleaned = cleaned.replace(/[\u{1F600}-\u{1F64F}]/gu, '');
  cleaned = cleaned.replace(/[\u{1F300}-\u{1F5FF}]/gu, '');
  cleaned = cleaned.replace(/[\u{1F680}-\u{1F6FF}]/gu, '');
  cleaned = cleaned.replace(/[\u{1F1E0}-\u{1F1FF}]/gu, '');
  cleaned = cleaned.replace(/[\u{2600}-\u{26FF}]/gu, '');
  cleaned = cleaned.replace(/[\u{2700}-\u{27BF}]/gu, '');
  cleaned = cleaned.replace(/[\u{1F900}-\u{1F9FF}]/gu, '');
  cleaned = cleaned.replace(/[\u{1FA70}-\u{1FAFF}]/gu, '');

  // Remove problematic Unicode ranges but preserve more formatting
  cleaned = cleaned.replace(/[\u{2000}-\u{200F}]/gu, ' '); // Spaces to regular space
  cleaned = cleaned.replace(/[\u{2028}-\u{202F}]/gu, ' '); // Line/paragraph separators
  cleaned = cleaned.replace(/[\u{2060}-\u{206F}]/gu, ''); // Invisible characters

  // Remove foreign characters but be less aggressive than titles
  // Allow more extended Latin characters for proper nouns
  cleaned = cleaned.replace(/[^\u0020-\u007E\u00A0-\u017F\u0180-\u024F]/g, '');

  // Clean up excessive asterisks (keep single ones for emphasis)
  cleaned = cleaned.replace(/\*{3,}/g, '**'); // Triple+ asterisks to double
  cleaned = cleaned.replace(/\*{2}\s*\*{2}/g, '**'); // Spaced double asterisks

  // Clean up multiple spaces but preserve line breaks
  cleaned = cleaned.replace(/[ \t]+/g, ' '); // Multiple spaces/tabs to single space
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n'); // Multiple line breaks to double

  return cleaned.trim();
}

/**
 * Validate that text is SEO-friendly
 * Returns true if text passes SEO character requirements
 */
export function validateSEOText(text: string): boolean {
  if (!text) return false;

  // Check for problematic characters
  const hasEmojis =
    /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{1F900}-\u{1F9FF}\u{1FA70}-\u{1FAFF}]/gu.test(
      text
    );
  const hasExcessiveAsterisks = /\*{3,}/.test(text);
  const hasProblematicUnicode =
    /[\u{2000}-\u{206F}\u{2070}-\u{209F}\u{2190}-\u{21FF}\u{2200}-\u{22FF}]/gu.test(
      text
    );

  return !hasEmojis && !hasExcessiveAsterisks && !hasProblematicUnicode;
}

/**
 * Get a report of problematic characters found in text
 * Useful for debugging and monitoring
 */
export function getCharacterReport(text: string): {
  hasIssues: boolean;
  issues: string[];
  cleanedLength: number;
  originalLength: number;
} {
  const issues: string[] = [];
  const originalLength = text.length;

  if (/\*{3,}/.test(text)) {
    issues.push('Excessive asterisks found');
  }

  if (
    /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{1F900}-\u{1F9FF}\u{1FA70}-\u{1FAFF}]/gu.test(
      text
    )
  ) {
    issues.push('Emojis found');
  }

  if (
    /[\u{2000}-\u{206F}\u{2070}-\u{209F}\u{2190}-\u{21FF}\u{2200}-\u{22FF}]/gu.test(
      text
    )
  ) {
    issues.push('Problematic Unicode characters found');
  }

  if (/[^\u0020-\u007E\u00A0-\u017F\u0180-\u024F]/g.test(text)) {
    issues.push('Foreign characters found');
  }

  const cleaned = cleanContent(text);

  return {
    hasIssues: issues.length > 0,
    issues,
    cleanedLength: cleaned.length,
    originalLength,
  };
}

/**
 * Clean and validate title with detailed reporting
 */
export function processTitle(title: string): {
  cleaned: string;
  isValid: boolean;
  report: ReturnType<typeof getCharacterReport>;
} {
  const cleaned = cleanTitle(title);
  const isValid = validateSEOText(cleaned);
  const report = getCharacterReport(title);

  return {
    cleaned,
    isValid,
    report,
  };
}

/**
 * Clean and validate content with detailed reporting
 */
export function processContent(content: string): {
  cleaned: string;
  isValid: boolean;
  report: ReturnType<typeof getCharacterReport>;
} {
  const cleaned = cleanContent(content);
  const isValid = validateSEOText(cleaned);
  const report = getCharacterReport(content);

  return {
    cleaned,
    isValid,
    report,
  };
}
