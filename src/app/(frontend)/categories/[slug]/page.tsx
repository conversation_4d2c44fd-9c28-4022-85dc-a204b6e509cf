import { Suspense } from 'react';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import {
  getCachedCategoryArticles,
  getCachedCategorySlugs,
} from '@/lib/cache/categories';
import CategoryPageLayout from '@/components/categories/CategoryPageLayout';
import CategoryAccessibilityNav from '@/components/categories/CategoryAccessibilityNav';
import CategoryPageSkeleton from '@/components/categories/CategoryPageSkeleton';

interface CategoryPageProps {
  params: Promise<{ slug: string }>;
}

// Generate metadata for SEO
export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;

  try {
    const getCachedData = getCachedCategoryArticles(slug);
    const categoryData = await getCachedData();

    // ✅ FIX: Handle null case (category not found)
    if (!categoryData) {
      console.error(`Error generating metadata for category: ${slug}`);
      notFound();
    }

    const { categoryInfo, totalArticles } = categoryData;

    const title = categoryInfo.title;
    const description = `Aktuelle ${title}-Na<PERSON>richten und Analysen. ${totalArticles} Artikel verfügbar.`;

    return {
      title: `${title} | Börsen Blick`,
      description,
      openGraph: {
        title: `${title} - Börsen Blick`,
        description,
        type: 'website',
        url: `https://borsenblick.de/categories/${slug}`,
      },
      twitter: {
        card: 'summary_large_image',
        title: `${title} - Börsen Blick`,
        description,
      },
      alternates: {
        canonical: `https://borsenblick.de/categories/${slug}`,
      },
    };
  } catch (error) {
    console.error(`Error generating metadata for category: ${slug}`, error);
    return {
      title: 'Category Not Found | Börsen Blick',
      description: 'The requested category could not be found.',
    };
  }
}

// Generate static params for published categories using cached data
export async function generateStaticParams(): Promise<{ slug: string }[]> {
  try {
    return await getCachedCategorySlugs();
  } catch (error) {
    console.error('Error generating category static params:', error);
    return [];
  }
}

// Enable Next.js ISR (Incremental Static Regeneration)
export const revalidate = 300; // 5 minutes

// Main category page component
export default async function CategoryPage({ params }: CategoryPageProps) {
  const { slug } = await params;

  // Validate category exists by attempting to fetch data
  try {
    const getCachedData = getCachedCategoryArticles(slug);
    const categoryData = await getCachedData();

    // ✅ FIX: Check if category data is null (category not found)
    if (!categoryData) {
      console.error(`Category not found: ${slug}`);
      notFound();
    }
  } catch (error) {
    console.error(`Error fetching category: ${slug}`, error);
    notFound();
  }

  return (
    <div className="min-h-dvh bg-background">
      {/* Accessibility Navigation */}
      <CategoryAccessibilityNav categorySlug={slug} />

      {/* Main Content */}
      <main
        id="main-content"
        className="focus:outline-none"
        tabIndex={-1}
        role="main"
        aria-label={`Category page`}
      >
        {/* Page Title - Hidden but accessible */}
        <h1 className="sr-only">Category Articles</h1>

        {/* Category Page Layout with Suspense */}
        <Suspense fallback={<CategoryPageSkeleton />}>
          <CategoryPageLayout categorySlug={slug} />
        </Suspense>
      </main>
    </div>
  );
}
