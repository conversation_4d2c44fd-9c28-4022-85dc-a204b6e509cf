import { Suspense } from 'react';
import type { <PERSON>ada<PERSON>, Viewport } from 'next';
import { AccessibilityNav } from '@/components/homepage/AccessibilityNav';
import TierOneSection from '@/components/homepage/TierOneSection';
import TierTwoSection from '@/components/homepage/TierTwoSection';
import TierThreeSection from '@/components/homepage/TierThreeSection';
import {
  TierOneSkeleton,
  TierTwoSkeleton,
  TierThreeSkeleton,
} from '@/components/homepage/TierSkeletons';
import { DEFAULT_HOMEPAGE_CONFIG } from '@/components/homepage/types';
import './global.css';

// Enable Next.js ISR (Incremental Static Regeneration)
export const revalidate = 300; // 5 minutes

// Page metadata
export const metadata: Metadata = {
  metadataBase: new URL('https://borsenblick.de'),
  title: '<PERSON><PERSON><PERSON> Blick - Deutsche Finanz- und Wirtschaftsnachrichten',
  description:
    'Aktuelle Finanz- und Wirtschaftsnachrichten aus Deutschland. <PERSON><PERSON><PERSON><PERSON>, Börsenberichte und Wirtschaftstrends.',
  keywords:
    'Börse,Finanzen,Wirtschaft,Deutschland,Marktanalyse,Aktien,Investment',
  authors: [{ name: 'Börsen Blick Team' }],
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'Börsen Blick - Deutsche Finanz- und Wirtschaftsnachrichten',
    description: 'Aktuelle Finanz- und Wirtschaftsnachrichten aus Deutschland',
    url: 'https://borsenblick.de',
    siteName: 'Börsen Blick',
    locale: 'de_DE',
    type: 'website',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Börsen Blick - Deutsche Finanz- und Wirtschaftsnachrichten',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Börsen Blick',
    description: 'Deutsche Finanz- und Wirtschaftsnachrichten',
    images: ['/twitter-image.jpg'],
  },
  alternates: {
    canonical: 'https://borsenblick.de',
  },
  category: 'news',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
};

export default async function HomePage() {
  const config = DEFAULT_HOMEPAGE_CONFIG;

  return (
    <div className="min-h-dvh bg-background">
      {/* Accessibility Navigation */}
      <AccessibilityNav
        config={config.accessibility}
        isLoading={false}
        hasError={false}
      />

      {/* Main Content */}
      <main
        id="main-content"
        className="focus:outline-none"
        tabIndex={-1}
        role="main"
        aria-label="Börsen Blick Homepage"
      >
        {/* Page Title - Hidden but accessible */}
        <h1 className="sr-only">
          Börsen Blick - Deutsche Finanz- und Wirtschaftsnachrichten
        </h1>

        {/* Homepage Grid Layout */}
        <div className="max-w-[1440px] mx-auto px-4 py-6 sm:py-8 md:py-10 lg:py-12">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">
            {/* Column 1 - Latest Updates (Tier 3) */}
            <aside
              className="sm:border-r-2 lg:border-r-2 xl:border-r-2 sm:border-gray-50 lg:border-gray-50 xl:border-gray-50 sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-3 sm:order-1"
              aria-labelledby="latest-updates-heading"
            >
              <h2 id="latest-updates-heading" className="sr-only">
                Aktuelle Meldungen
              </h2>
              <Suspense fallback={<TierThreeSkeleton />}>
                <TierThreeSection />
              </Suspense>
            </aside>

            {/* Columns 2-3 - Tier 1 Only (Enhanced Featured Content) */}
            <section
              id="featured-articles"
              className="sm:col-span-1 lg:col-span-2 xl:col-span-2 sm:border-r-2 lg:border-r-2 xl:border-r-2 sm:border-gray-50 lg:border-gray-50 xl:border-gray-50 sm:px-2 lg:px-3 xl:px-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-1 sm:order-2"
              aria-labelledby="featured-articles-heading"
            >
              <h2 id="featured-articles-heading" className="sr-only">
                Wichtige Artikel
              </h2>

              {/* Enhanced Tier 1 - Prominent Post + Grid + Horizontal */}
              <Suspense fallback={<TierOneSkeleton />}>
                <TierOneSection />
              </Suspense>
            </section>

            {/* Column 4 - Market Analysis (Tier 2) */}
            <aside
              className="xl:pl-4 order-2 sm:order-3 hidden xl:block"
              aria-labelledby="market-analysis-heading"
            >
              <h2 id="market-analysis-heading" className="sr-only">
                Marktanalyse
              </h2>
              <Suspense fallback={<TierTwoSkeleton />}>
                <TierTwoSection />
              </Suspense>
            </aside>
          </div>
        </div>
      </main>
    </div>
  );
}
