import config from '@payload-config';
import { NextResponse } from 'next/server';
import { getPayload } from 'payload';
import { getFirecrawlStats } from '@/lib/integrations/firecrawl/enhanced-client';
import { rssProcessingService } from '@/utilities/RSSProcessingService';

/**
 * Production Content Pipeline API
 *
 * Executes the complete RSS-to-articles workflow:
 * 1. Fetch all active RSS feeds
 * 2. Parse RSS content and match keywords
 * 3. Extract full content via Firecrawl
 * 4. Calculate Canadian market relevance via AI
 * 5. Create candidate articles for accepted content
 *
 * This endpoint replaces scattered test endpoints with a single,
 * production-ready pipeline that mimics the actual workflow.
 */

export async function POST() {
  const startTime = Date.now();

  try {
    console.log('🚀 Starting production content pipeline...');
    console.log(`⏰ Start time: ${new Date(startTime).toISOString()}`);

    const payload = await getPayload({ config });

    // Step 1: Validate database state
    console.log('🔍 Validating database state...');

    const [rssFeeds, keywords, categories] = await Promise.all([
      payload.find({
        collection: 'rss-feeds',
        where: { isActive: { equals: true } },
        limit: 100,
      }),
      payload.find({
        collection: 'keywords' as any,
        where: { isActive: { equals: true } },
        limit: 100,
      }),
      payload.find({ collection: 'categories', limit: 100 }),
    ]);

    if (rssFeeds.totalDocs === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No active RSS feeds found',
          message:
            'Please create RSS feeds first using: node scripts/content-load.mjs',
        },
        { status: 400 }
      );
    }

    if (keywords.totalDocs === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No keywords found',
          message:
            'Please create keywords first using: node scripts/content-load.mjs',
        },
        { status: 400 }
      );
    }

    console.log(`✅ Database validation passed:`);
    console.log(`   - Active RSS feeds: ${rssFeeds.totalDocs}`);
    rssFeeds.docs.forEach((feed: any, index: number) => {
      console.log(
        `     ${index + 1}. ${feed.name} (${feed.priority}) - ${feed.language}`
      );
    });
    console.log(`   - Active keywords: ${keywords.totalDocs}`);
    const keywordList = keywords.docs
      .map((k: any) => k.keyword)
      .slice(0, 10)
      .join(', ');
    console.log(
      `     Keywords: ${keywordList}${keywords.totalDocs > 10 ? '...' : ''}`
    );
    console.log(`   - Categories: ${categories.totalDocs}`);

    // Step 2: Execute RSS processing pipeline
    console.log('\n📡 Executing RSS processing pipeline...');
    console.log(
      '🔄 Processing feeds in priority order (high → medium → low)...'
    );

    const processingResult = await rssProcessingService.processAllFeeds();

    // Step 3: Calculate processing time
    const processingTime = Date.now() - startTime;
    const processingTimeSeconds = Math.round(processingTime / 1000);

    // Step 4: Get updated article counts
    const articleStats = await Promise.all([
      payload.find({
        collection: 'articles',
        where: { workflowStage: { equals: 'candidate-article' } },
        limit: 1000,
      }),
      payload.find({
        collection: 'articles',
        where: { _status: { equals: 'published' } },
        limit: 1000,
      }),
      payload.find({ collection: 'articles', limit: 1000 }),
    ]);

    // Step 5: Get Firecrawl API usage stats
    const firecrawlStats = getFirecrawlStats();

    // Step 6: Prepare comprehensive response
    const response = {
      success: processingResult.success,
      message: 'Content pipeline execution completed',
      timing: {
        processingTimeMs: processingTime,
        processingTimeSeconds: processingTimeSeconds,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date().toISOString(),
      },
      pipeline: {
        processed: processingResult.processed,
        accepted: processingResult.accepted,
        rejected: processingResult.rejected,
        errorCount: processingResult.errors.length,
        successRate:
          processingResult.processed > 0
            ? Math.round(
                (processingResult.accepted / processingResult.processed) * 100
              )
            : 0,
      },
      firecrawl: {
        totalRequests: firecrawlStats.totalRequests,
        successfulRequests: firecrawlStats.successfulRequests,
        failedRequests: firecrawlStats.failedRequests,
        successRate:
          firecrawlStats.totalRequests > 0
            ? Math.round(
                (firecrawlStats.successfulRequests /
                  firecrawlStats.totalRequests) *
                  100
              )
            : 0,
        errors: {
          rateLimits: firecrawlStats.rateLimitErrors,
          configuration: firecrawlStats.configErrors,
          authentication: firecrawlStats.authErrors,
          timeouts: firecrawlStats.timeoutErrors,
        },
      },
      database: {
        activeFeeds: rssFeeds.totalDocs,
        activeKeywords: keywords.totalDocs,
        totalCategories: categories.totalDocs,
        candidateArticles: articleStats[0].totalDocs,
        publishedArticles: articleStats[1].totalDocs,
        totalArticles: articleStats[2].totalDocs,
      },
      feeds: rssFeeds.docs.map((feed: any) => ({
        id: feed.id,
        name: feed.name,
        url: feed.url,
        language: feed.language,
        priority: feed.priority,
      })),
      details: processingResult.details.slice(0, 20), // Show first 20 items
      errors: processingResult.errors,
      adminUrls: {
        articles: 'http://localhost:3000/admin/collections/articles',
        candidateArticles:
          'http://localhost:3000/admin/collections/articles?where%5Bstatus%5D%5Bequals%5D=candidate-article',
        rssFeeds: 'http://localhost:3000/admin/collections/rss-feeds',
        keywords: 'http://localhost:3000/admin/collections/keywords',
      },
    };

    // Step 7: Log summary
    console.log('\n📊 Pipeline Execution Summary:');
    console.log(`   ⏱️  Processing time: ${processingTimeSeconds}s`);
    console.log(`   📄 Items processed: ${processingResult.processed}`);
    console.log(`   ✅ Articles accepted: ${processingResult.accepted}`);
    console.log(`   ❌ Articles rejected: ${processingResult.rejected}`);
    console.log(`   🎯 Success rate: ${response.pipeline.successRate}%`);
    console.log(`   📚 Total articles in DB: ${articleStats[2].totalDocs}`);

    // Log Firecrawl summary
    if (firecrawlStats.totalRequests > 0) {
      console.log('\n🔥 Firecrawl API Summary:');
      console.log(`   📡 Total requests: ${firecrawlStats.totalRequests}`);
      console.log(
        `   ✅ Successful: ${firecrawlStats.successfulRequests} (${response.firecrawl.successRate}%)`
      );
      console.log(`   ❌ Failed: ${firecrawlStats.failedRequests}`);

      if (firecrawlStats.failedRequests > 0) {
        console.log('   📋 Error breakdown:');
        if (firecrawlStats.rateLimitErrors > 0)
          console.log(
            `      ⏳ Rate limits: ${firecrawlStats.rateLimitErrors}`
          );
        if (firecrawlStats.configErrors > 0)
          console.log(`      ⚙️ Config errors: ${firecrawlStats.configErrors}`);
        if (firecrawlStats.authErrors > 0)
          console.log(`      🔐 Auth errors: ${firecrawlStats.authErrors}`);
        if (firecrawlStats.timeoutErrors > 0)
          console.log(`      ⏰ Timeouts: ${firecrawlStats.timeoutErrors}`);
      }
    }

    if (processingResult.errors.length > 0) {
      console.log(`\n⚠️  Processing Errors: ${processingResult.errors.length}`);
      processingResult.errors.forEach((error, index) => {
        console.log(`      ${index + 1}. ${error}`);
      });
    }

    console.log('\n🎉 Content pipeline completed successfully!');

    return NextResponse.json(response);
  } catch (error: any) {
    const processingTime = Date.now() - startTime;
    console.error('❌ Content pipeline failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        message: 'Content pipeline execution failed',
        timing: {
          processingTimeMs: processingTime,
          processingTimeSeconds: Math.round(processingTime / 1000),
          startTime: new Date(startTime).toISOString(),
          endTime: new Date().toISOString(),
        },
        troubleshooting: {
          commonIssues: [
            'Ensure database is running: pnpm db:start',
            'Ensure content is loaded: node scripts/content-load.mjs',
            'Check RSS feed URLs are accessible',
            'Verify OpenAI API key is configured',
            'Verify Firecrawl API key is configured',
          ],
          adminUrl: 'http://localhost:3000/admin',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint for pipeline status and configuration
 */
export async function GET() {
  try {
    const payload = await getPayload({ config });

    const [rssFeeds, keywords, categories, articles] = await Promise.all([
      payload.find({ collection: 'rss-feeds', limit: 100 }),
      payload.find({ collection: 'keywords' as any, limit: 100 }),
      payload.find({ collection: 'categories', limit: 100 }),
      payload.find({ collection: 'articles', limit: 1000 }),
    ]);

    const candidateArticles = await payload.find({
      collection: 'articles',
      where: { workflowStage: { equals: 'candidate-article' } },
      limit: 1000,
    });

    const publishedArticles = await payload.find({
      collection: 'articles',
      where: { _status: { equals: 'published' } },
      limit: 1000,
    });

    return NextResponse.json({
      success: true,
      message: 'Content pipeline status',
      configuration: {
        totalRssFeeds: rssFeeds.totalDocs,
        activeRssFeeds: rssFeeds.docs.filter((feed: any) => feed.isActive)
          .length,
        totalKeywords: keywords.totalDocs,
        activeKeywords: keywords.docs.filter((keyword: any) => keyword.isActive)
          .length,
        totalCategories: categories.totalDocs,
      },
      database: {
        totalArticles: articles.totalDocs,
        candidateArticles: candidateArticles.totalDocs,
        publishedArticles: publishedArticles.totalDocs,
      },
      readyToRun:
        rssFeeds.docs.filter((feed: any) => feed.isActive).length > 0 &&
        keywords.docs.filter((keyword: any) => keyword.isActive).length > 0,
      adminUrls: {
        articles: 'http://localhost:3000/admin/collections/articles',
        rssFeeds: 'http://localhost:3000/admin/collections/rss-feeds',
        keywords: 'http://localhost:3000/admin/collections/keywords',
      },
    });
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        message: 'Failed to get pipeline status',
      },
      { status: 500 }
    );
  }
}
