{"name": "b<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write src", "format:check": "prettier --check src", "lint:all": "pnpm lint && pnpm format:check", "fix:all": "pnpm lint:fix && pnpm format", "lint:next": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "check:openai": "node scripts/check-openai-status.js", "migrate:create": "cross-env NODE_OPTIONS=--no-deprecation payload migrate:create", "migrate": "cross-env NODE_OPTIONS=--no-deprecation payload migrate", "migrate:status": "cross-env NODE_OPTIONS=--no-deprecation payload migrate:status", "migrate:fresh": "cross-env NODE_OPTIONS=--no-deprecation payload migrate:fresh", "migrate:reset": "cross-env NODE_OPTIONS=--no-deprecation payload migrate:reset", "ci": "cross-env NODE_OPTIONS=--no-deprecation payload migrate && pnpm build"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@mendable/firecrawl-js": "^1.25.5", "@payloadcms/db-postgres": "3.43.0", "@payloadcms/next": "3.43.0", "@payloadcms/payload-cloud": "3.43.0", "@payloadcms/plugin-seo": "3.43.0", "@payloadcms/richtext-lexical": "3.43.0", "@payloadcms/ui": "3.43.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.4", "@tailwindcss/postcss": "^4.1.10", "@types/jsdom": "^21.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "dotenv": "^17.0.1", "graphql": "^16.8.1", "lucide-react": "^0.525.0", "next": "15.3.0", "next-themes": "^0.4.6", "openai": "^5.5.1", "payload": "3.43.0", "pg": "^8.16.3", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.58.1", "rss-parser": "^3.13.0", "sharp": "0.32.6", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "zod": "^3.25.67"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.5.4", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.7.0", "@vitest/ui": "^3.2.4", "eslint": "^8.57.1", "eslint-config-next": "^15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "tw-animate-css": "^1.3.4", "typescript": "5.7.3", "vitest": "^3.2.4"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}