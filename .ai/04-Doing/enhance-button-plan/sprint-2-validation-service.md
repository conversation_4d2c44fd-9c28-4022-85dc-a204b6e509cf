# Sprint 2: Centralized Validation Service

**Duration**: 2-3 hours  
**Priority**: High  
**Dependencies**: Sprint 1 (Field Access & Debugging)

## 🎯 Sprint Goals

1. **Create centralized validation service** with confirmed business rules
2. **Implement button text logic** (Re-Enhance, Re-Translate)
3. **Add button visibility logic** based on article type
4. **Update DocumentControls** to use centralized validation

## 📋 Tasks

### Task 2.1: Create Validation Service (60 minutes)
**File**: `src/lib/services/article-validation.ts` (new file)

**Business Rules** (confirmed requirements):
- **Generated Articles**: No enhance button, translate button with enhanced field validation
- **Curated Articles**: Both buttons available, 20+ character validation for all fields
- **Button Text**: Changes to "Re-Enhance" and "Re-Translate" after first operation

```typescript
export interface ArticleValidationContext {
  articleType: 'generated' | 'curated';
  workflowStage: string;
  hasBeenEnhanced: boolean;
  hasGermanTranslation: boolean;
  hasOriginalSource: boolean; // Track if article has original RSS source
  fields: {
    // Main article fields
    title?: string;
    
    // Enhanced/Content fields (primary content for both types)
    enhancedTitle?: string;
    enhancedSummary?: string;
    enhancedContent?: any;
    
    // Source fields (ONLY for generated articles or converted articles)
    originalTitle?: string;
    originalContent?: any;
    originalSummary?: string;
    
    // German fields (for re-translation detection)
    germanTitle?: string;
    germanContent?: any;
  };
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  buttonText?: string;
}

/**
 * Validates if article can be enhanced
 * NEW REQUIREMENT: Only curated articles can be enhanced, using enhanced fields as input
 */
export function validateForEnhancement(
  context: ArticleValidationContext
): ValidationResult {
  const { articleType, fields, hasBeenEnhanced } = context;
  const errors: string[] = [];

  // Generated articles cannot be enhanced (already enhanced by RSS pipeline)
  if (articleType === 'generated') {
    return {
      isValid: false,
      errors: [
        'Generated articles are already enhanced and cannot be re-enhanced',
      ],
      warnings: [],
    };
  }

  // For curated articles, validate enhanced fields (20+ character requirement)
  // CONFIRMED: Curated articles use enhanced fields as input for enhancement
  if (!fields.enhancedTitle || fields.enhancedTitle.length < 20) {
    errors.push('Title must be at least 20 characters');
  }
  
  if (!fields.enhancedSummary || fields.enhancedSummary.length < 20) {
    errors.push('Summary must be at least 20 characters');
  }
  
  if (!fields.enhancedContent) {
    errors.push('Content is required for enhancement');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
    buttonText: hasBeenEnhanced ? 'Re-Enhance' : 'Enhance Content',
  };
}

/**
 * Validates if article can be translated
 * NEW REQUIREMENT: Both generated and curated can translate, different validation rules
 */
export function validateForTranslation(
  context: ArticleValidationContext
): ValidationResult {
  const { articleType, fields, hasGermanTranslation } = context;
  const errors: string[] = [];

  if (articleType === 'generated') {
    // Generated articles: validate enhanced fields (should be pre-populated)
    if (!fields.enhancedTitle || fields.enhancedTitle.length < 20) {
      errors.push('Enhanced title must be at least 20 characters');
    }
    if (!fields.enhancedSummary || fields.enhancedSummary.length < 20) {
      errors.push('Enhanced summary must be at least 20 characters');
    }
    if (!fields.enhancedContent) {
      errors.push('Enhanced content is required');
    }
  } else if (articleType === 'curated') {
    // Curated articles: validate enhanced fields (user can translate without enhancing first)
    if (!fields.enhancedTitle || fields.enhancedTitle.length < 20) {
      errors.push('Enhanced title must be at least 20 characters');
    }
    if (!fields.enhancedSummary || fields.enhancedSummary.length < 20) {
      errors.push('Enhanced summary must be at least 20 characters');
    }
    if (!fields.enhancedContent) {
      errors.push('Enhanced content is required');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
    buttonText: hasGermanTranslation ? 'Re-Translate' : 'Translate to German',
  };
}

/**
 * Determines button visibility based on article type and state
 */
export function getButtonVisibility(context: ArticleValidationContext): {
  showEnhanceButton: boolean;
  showTranslateButton: boolean;
} {
  const { articleType } = context;

  return {
    // Enhance button: Only show for curated articles
    showEnhanceButton: articleType === 'curated',
    
    // Translate button: Show for all article types
    showTranslateButton: true,
  };
}
```

### Task 2.2: Update DocumentControls to Use Validation Service (45 minutes)
**File**: `src/components/admin/article-actions/DocumentControls.tsx`

```typescript
import {
  validateForEnhancement,
  validateForTranslation,
  getButtonVisibility,
} from '@/lib/services/article-validation';

// Replace existing validation functions with centralized service:
const articleContext = useMemo(() => ({
  articleType,
  workflowStage,
  hasBeenEnhanced,
  hasGermanTranslation: !!germanTitle || !!germanContent,
  hasOriginalSource: false, // TODO: Implement in Sprint 5
  fields: {
    title,
    enhancedTitle,
    enhancedSummary,
    enhancedContent,
    germanTitle,
    germanContent,
  },
}), [articleType, workflowStage, hasBeenEnhanced, title, enhancedTitle, enhancedSummary, enhancedContent, germanTitle, germanContent]);

const enhanceValidation = validateForEnhancement(articleContext);
const translateValidation = validateForTranslation(articleContext);
const buttonVisibility = getButtonVisibility(articleContext);

// Update button logic to use validation results:
const canEnhanceContent = !!id && enhanceValidation.isValid && !isEnhancing && !isTranslating;
const canTranslate = !!id && translateValidation.isValid && !isTranslating && !isEnhancing;

// Update validation message function:
const getValidationMessage = useCallback(() => {
  if (!id) return 'Please save the article first';
  if (buttonVisibility.showEnhanceButton && !enhanceValidation.isValid) {
    return enhanceValidation.errors[0];
  }
  if (!translateValidation.isValid) return translateValidation.errors[0];
  return null;
}, [id, enhanceValidation, translateValidation, buttonVisibility]);
```

### Task 2.3: Add Browser Tools Testing (30 minutes)

**Testing with Browser Tools**:
1. Monitor validation results in console
2. Test button text changes (Re-Enhance, Re-Translate)
3. Verify button visibility for different article types
4. Test validation error messages

**Console Debug Addition**:
```typescript
// Add validation debugging
console.log('🔍 Validation Debug:', {
  enhanceValidation,
  translateValidation,
  buttonVisibility,
  canEnhanceContent,
  canTranslate,
  validationMessage: getValidationMessage(),
});
```

### Task 2.4: Unit Tests (45 minutes)
**File**: `src/lib/services/__tests__/article-validation.test.ts`

Test scenarios:
- [ ] Generated article validation (no enhance, translate available)
- [ ] Curated article validation (both buttons available)
- [ ] Character count validation (20+ requirement)
- [ ] Button text logic (Re-Enhance, Re-Translate)
- [ ] Button visibility logic

## 🧪 Testing Checklist

- [ ] Generated articles: No enhance button, translate button visible
- [ ] Curated articles: Both buttons visible
- [ ] Validation errors show correct messages
- [ ] Button text changes after operations
- [ ] 20+ character validation works correctly
- [ ] Console debugging shows validation state

## 📊 Success Criteria

1. **Centralized Validation**: All validation logic in one service
2. **Business Rules**: Correct behavior for generated vs curated articles
3. **Button Text**: Dynamic text based on operation history
4. **Error Messages**: Clear, helpful validation messages
5. **Button Visibility**: Correct visibility based on article type

## 🔄 Next Sprint

**Sprint 3**: API Response Standardization - Standardize API responses to work with the new validation system.

## 📝 Notes

- Focus on getting business rules exactly right
- Use browser tools to test validation in real-time
- Ensure button text changes are user-friendly
- Document any edge cases discovered during testing
