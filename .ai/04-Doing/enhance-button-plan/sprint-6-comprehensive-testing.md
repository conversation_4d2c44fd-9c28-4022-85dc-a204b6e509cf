# Sprint 6: Comprehensive Testing & Validation

**Duration**: 3-4 hours  
**Priority**: High  
**Dependencies**: All previous sprints (1-5)

## 🎯 Sprint Goals

1. **Create comprehensive test suite** covering all scenarios from requirements table
2. **Implement regression tests** for broken use cases
3. **Validate all requirements** from the master plan
4. **Performance testing** and optimization
5. **Final integration testing** with browser tools

## 📋 Tasks

### Task 6.1: Component Integration Tests (90 minutes)
**File**: `src/components/admin/article-actions/__tests__/DocumentControls.test.tsx`

**Test Coverage**: All scenarios from requirements table

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ArticleDocumentControls } from '../DocumentControls';

// Mock PayloadCMS hooks
const mockUseFormFields = vi.fn();
const mockDispatchFields = vi.fn();
const mockUseDocumentInfo = vi.fn();

vi.mock('@payloadcms/ui', () => ({
  useFormFields: mockUseFormFields,
  useAllFormFields: () => [mockFields, mockDispatchFields],
  useDocumentInfo: mockUseDocumentInfo,
  useToast: () => ({ toast: { success: vi.fn(), error: vi.fn() } }),
}));

describe('DocumentControls - Requirements Table Scenarios', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseDocumentInfo.mockReturnValue({ id: 'test-article-id' });
  });

  describe('Generated Article (Default)', () => {
    beforeEach(() => {
      mockUseFormFields
        .mockReturnValueOnce('generated') // articleType
        .mockReturnValueOnce('enhanced-draft') // workflowStage
        .mockReturnValueOnce(true) // hasBeenEnhanced
        .mockReturnValueOnce('Enhanced Title with enough characters') // enhancedTitle
        .mockReturnValueOnce('Enhanced summary with enough characters for validation') // enhancedSummary
        .mockReturnValueOnce({ content: 'Enhanced content' }) // enhancedContent
        .mockReturnValueOnce('') // germanTitle
        .mockReturnValueOnce(null); // germanContent
    });

    it('should hide enhance button for generated articles', () => {
      render(<ArticleDocumentControls />);
      expect(screen.queryByText(/enhance/i)).not.toBeInTheDocument();
    });

    it('should show active translate button when enhanced fields >20 chars', () => {
      render(<ArticleDocumentControls />);
      const translateButton = screen.getByText(/translate/i);
      expect(translateButton).toBeInTheDocument();
      expect(translateButton).not.toBeDisabled();
    });

    it('should show "Re-Translate" after translation', () => {
      mockUseFormFields
        .mockReturnValueOnce('generated')
        .mockReturnValueOnce('translated')
        .mockReturnValueOnce(true)
        .mockReturnValueOnce('Enhanced Title')
        .mockReturnValueOnce('Enhanced summary')
        .mockReturnValueOnce({ content: 'Enhanced content' })
        .mockReturnValueOnce('German Title') // Has German content
        .mockReturnValueOnce({ content: 'German content' });

      render(<ArticleDocumentControls />);
      expect(screen.getByText(/re-translate/i)).toBeInTheDocument();
    });
  });

  describe('Generated → Curated Switch', () => {
    beforeEach(() => {
      mockUseFormFields
        .mockReturnValueOnce('curated') // Switched to curated
        .mockReturnValueOnce('enhanced-draft')
        .mockReturnValueOnce(true)
        .mockReturnValueOnce('Enhanced Title with enough characters')
        .mockReturnValueOnce('Enhanced summary with enough characters')
        .mockReturnValueOnce({ content: 'Enhanced content' })
        .mockReturnValueOnce('')
        .mockReturnValueOnce(null)
        .mockReturnValueOnce(true); // hasOriginalSource = true (converted)
    });

    it('should show both buttons when switched to curated', () => {
      render(<ArticleDocumentControls />);
      expect(screen.getByText(/enhance/i)).toBeInTheDocument();
      expect(screen.getByText(/translate/i)).toBeInTheDocument();
    });

    it('should enable both buttons when validation passes', () => {
      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);
      const translateButton = screen.getByText(/translate/i);
      expect(enhanceButton).not.toBeDisabled();
      expect(translateButton).not.toBeDisabled();
    });
  });

  describe('New Curated Article', () => {
    beforeEach(() => {
      mockUseFormFields
        .mockReturnValueOnce('curated')
        .mockReturnValueOnce('curated-draft')
        .mockReturnValueOnce(false)
        .mockReturnValueOnce('Short') // <20 characters
        .mockReturnValueOnce('Short') // <20 characters
        .mockReturnValueOnce(null)
        .mockReturnValueOnce('')
        .mockReturnValueOnce(null)
        .mockReturnValueOnce(false); // hasOriginalSource = false (new)
    });

    it('should show both buttons but disabled for insufficient content', () => {
      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);
      const translateButton = screen.getByText(/translate/i);
      expect(enhanceButton).toBeInTheDocument();
      expect(translateButton).toBeInTheDocument();
      expect(enhanceButton).toBeDisabled();
      expect(translateButton).toBeDisabled();
    });
  });

  describe('Curated - Valid Content', () => {
    beforeEach(() => {
      mockUseFormFields
        .mockReturnValueOnce('curated')
        .mockReturnValueOnce('curated-draft')
        .mockReturnValueOnce(false)
        .mockReturnValueOnce('Valid title with enough characters for validation')
        .mockReturnValueOnce('Valid summary with enough characters for validation')
        .mockReturnValueOnce({ content: 'Valid content' })
        .mockReturnValueOnce('')
        .mockReturnValueOnce(null)
        .mockReturnValueOnce(false);
    });

    it('should enable both buttons when validation passes', () => {
      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);
      const translateButton = screen.getByText(/translate/i);
      expect(enhanceButton).not.toBeDisabled();
      expect(translateButton).not.toBeDisabled();
    });

    it('should call enhance API when enhance button clicked', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          message: 'Enhanced successfully',
          data: { englishTab: { enhancedTitle: 'Enhanced Title' } }
        }),
      });

      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);
      
      fireEvent.click(enhanceButton);
      
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/articles/enhance', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ articleId: 'test-article-id' }),
        });
      });
    });
  });

  describe('Mutual Exclusion', () => {
    it('should disable translate button when enhance is running', async () => {
      // Mock slow API response
      global.fetch = vi.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({ success: true, data: {} })
        }), 100))
      );

      render(<ArticleDocumentControls />);
      const enhanceButton = screen.getByText(/enhance/i);
      const translateButton = screen.getByText(/translate/i);
      
      fireEvent.click(enhanceButton);
      
      // During operation, translate should be disabled
      expect(translateButton).toBeDisabled();
    });
  });
});
```

### Task 6.2: Validation Service Tests (60 minutes)
**File**: `src/lib/services/__tests__/article-validation.test.ts`

```typescript
import { describe, it, expect } from 'vitest';
import {
  validateForEnhancement,
  validateForTranslation,
  getButtonVisibility,
  getTabVisibility,
} from '../article-validation';

describe('Article Validation Service', () => {
  describe('validateForEnhancement', () => {
    it('should reject generated articles', () => {
      const context = {
        articleType: 'generated' as const,
        workflowStage: 'enhanced-draft',
        hasBeenEnhanced: true,
        hasGermanTranslation: false,
        hasOriginalSource: true,
        fields: {
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: { content: 'Valid content' },
        },
      };

      const result = validateForEnhancement(context);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Generated articles are already enhanced and cannot be re-enhanced');
    });

    it('should validate curated articles with sufficient content', () => {
      const context = {
        articleType: 'curated' as const,
        workflowStage: 'curated-draft',
        hasBeenEnhanced: false,
        hasGermanTranslation: false,
        hasOriginalSource: false,
        fields: {
          enhancedTitle: 'Valid title with enough characters for validation',
          enhancedSummary: 'Valid summary with enough characters for validation',
          enhancedContent: { content: 'Valid content' },
        },
      };

      const result = validateForEnhancement(context);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.buttonText).toBe('Enhance Content');
    });

    it('should return "Re-Enhance" for previously enhanced articles', () => {
      const context = {
        articleType: 'curated' as const,
        workflowStage: 'enhanced-draft',
        hasBeenEnhanced: true,
        hasGermanTranslation: false,
        hasOriginalSource: false,
        fields: {
          enhancedTitle: 'Valid title with enough characters',
          enhancedSummary: 'Valid summary with enough characters',
          enhancedContent: { content: 'Valid content' },
        },
      };

      const result = validateForEnhancement(context);
      expect(result.buttonText).toBe('Re-Enhance');
    });
  });

  describe('getTabVisibility', () => {
    it('should show Sources tab for generated articles', () => {
      const context = {
        articleType: 'generated' as const,
        hasOriginalSource: true,
        // ... other fields
      };

      const result = getTabVisibility(context);
      expect(result.showSourcesTab).toBe(true);
    });

    it('should hide Sources tab for new curated articles', () => {
      const context = {
        articleType: 'curated' as const,
        hasOriginalSource: false,
        // ... other fields
      };

      const result = getTabVisibility(context);
      expect(result.showSourcesTab).toBe(false);
    });

    it('should show Sources tab for converted curated articles', () => {
      const context = {
        articleType: 'curated' as const,
        hasOriginalSource: true, // Converted from generated
        // ... other fields
      };

      const result = getTabVisibility(context);
      expect(result.showSourcesTab).toBe(true);
    });
  });
});
```

### Task 6.3: API Integration Tests (60 minutes)
**File**: `src/app/api/articles/__tests__/integration.test.ts`

Test API endpoints with standardized responses and form state updates.

### Task 6.4: Regression Tests (45 minutes)
**File**: `src/components/admin/article-actions/__tests__/regression.test.ts`

**Focus**: Test all previously broken use cases to prevent future regressions:

```typescript
describe('Regression Tests - Previously Broken Use Cases', () => {
  it('should not break when field access returns undefined', () => {
    // Test field access with missing data
  });

  it('should handle workflow stage transitions correctly', () => {
    // Test stage transitions after operations
  });

  it('should maintain button states during form updates', () => {
    // Test button state consistency
  });

  it('should handle API errors gracefully', () => {
    // Test error handling and recovery
  });
});
```

### Task 6.5: Browser Tools Integration Testing (60 minutes)

**Comprehensive Testing Process**:
1. Use browser-tools to navigate through all test scenarios
2. Monitor console logs for errors or warnings
3. Test real user workflows with browser monitoring
4. Validate network requests and responses
5. Test performance and loading states

**Test Scenarios**:
- [ ] Generated article workflow (enhance hidden, translate active)
- [ ] Curated article workflow (both buttons available)
- [ ] Article type switching (generated → curated)
- [ ] Re-operations (re-enhance, re-translate)
- [ ] Error handling and recovery
- [ ] Form state updates and synchronization

### Task 6.6: Performance Testing (30 minutes)

**Performance Metrics**:
- [ ] Field access performance (no unnecessary re-renders)
- [ ] Validation performance (efficient validation checks)
- [ ] API response times (enhance/translate operations)
- [ ] Form update performance (no UI lag)
- [ ] Memory usage (no memory leaks)

### Task 6.7: Requirements Validation (45 minutes)

**Final Validation Against Requirements Table**:

| Scenario | Expected Behavior | Test Status | Notes |
|----------|------------------|-------------|-------|
| Generated Article (Default) | ❌ No enhance, ✅ Translate active | ☐ Pass | |
| Generated → Curated Switch | ✅ Both buttons available | ☐ Pass | |
| New Curated Article | ✅ Both buttons, disabled until valid | ☐ Pass | |
| Curated - Valid Content | ✅ Both buttons active | ☐ Pass | |
| Re-Enhancement | ✅ "Re-Enhance" button text | ☐ Pass | |
| Re-Translation | ✅ "Re-Translate" button text | ☐ Pass | |
| Mutual Exclusion | 🚫 Cannot run simultaneously | ☐ Pass | |
| Sources Tab Logic | ✅ Conditional visibility | ☐ Pass | |

## 🧪 Testing Checklist

### Functional Testing
- [ ] All button visibility rules work correctly
- [ ] Validation logic matches business requirements
- [ ] API responses are properly formatted
- [ ] Form state updates work without page refresh
- [ ] Error handling provides good user feedback

### Integration Testing
- [ ] PayloadCMS integration works correctly
- [ ] Browser tools monitoring works
- [ ] Network requests are properly formatted
- [ ] Console logging provides useful debug info

### Regression Testing
- [ ] Previously broken use cases now work
- [ ] No new bugs introduced
- [ ] Performance hasn't degraded
- [ ] All edge cases handled

### User Experience Testing
- [ ] Loading states provide good feedback
- [ ] Notifications are helpful and timely
- [ ] Button states are clear and consistent
- [ ] Operations complete successfully

## 📊 Success Criteria

1. **100% Test Coverage**: All scenarios from requirements table tested
2. **Zero Regressions**: All previously broken use cases now work
3. **Performance**: No performance degradation from changes
4. **User Experience**: Smooth, responsive interface
5. **Requirements Met**: All confirmed requirements validated

## 🔄 Project Completion

**Final Deliverables**:
- [ ] All 6 sprints completed successfully
- [ ] Comprehensive test suite passing
- [ ] Requirements validation complete
- [ ] Documentation updated
- [ ] Code review completed

## 📝 Notes

- Focus on comprehensive coverage of all scenarios
- Use browser tools extensively for real-world testing
- Document any edge cases or unexpected behaviors
- Ensure all tests are maintainable and well-documented
- Validate that the solution meets all original requirements
