# Sprint 3: API Response Standardization

**Duration**: 2-3 hours  
**Priority**: High  
**Dependencies**: Sprint 1 (Field Access), Sprint 2 (Validation Service)

## 🎯 Sprint Goals

1. **Standardize API response formats** for enhance and translate endpoints
2. **Fix form state updates** to work with new field structure
3. **Implement proper workflow stage transitions** 
4. **Test API integration** with browser tools monitoring

## 📋 Tasks

### Task 3.1: Standardize API Response Format (60 minutes)
**Files**: 
- `src/app/api/articles/enhance/route.ts`
- `src/app/api/articles/translate/route.ts`

**Problem**: API responses don't match the format expected by the UI component for form updates.

**Solution**: Create consistent response format:

```typescript
// Standardize response format for both APIs:
interface StandardAPIResponse {
  success: boolean;
  message: string;
  data: {
    // Fields that match PayloadCMS structure
    englishTab?: {
      enhancedTitle?: string;
      enhancedSummary?: string;
      enhancedContent?: any;
      keyInsights?: string[];
      keywords?: string[];
    };
    germanTab?: {
      germanTitle?: string;
      germanSummary?: string;
      germanContent?: any;
      germanKeyInsights?: string[];
      germanKeywords?: string[];
    };
    // Main fields that get updated
    title?: string;
    slug?: string;
    workflowStage?: string;
    hasBeenEnhanced?: boolean;
    hasGermanTranslation?: boolean;
  };
  metrics?: {
    processingTime: number;
    [key: string]: any;
  };
  error?: string;
}
```

**Update enhance route response**:
```typescript
// src/app/api/articles/enhance/route.ts
return NextResponse.json({
  success: true,
  message: 'Article enhanced successfully',
  data: {
    englishTab: {
      enhancedTitle: cleanTitle,
      enhancedSummary: enhancedSummary,
      enhancedContent: enhancedContentLexical,
      keyInsights: keyInsights,
      keywords: enhancedData.enhancedContent.keywords,
    },
    title: cleanTitle, // Update main title field
    slug: generateSlug(cleanTitle), // Update slug
    workflowStage: 'enhanced-draft', // CONFIRMED: Rename from "Enhanced English (Candidate)"
    hasBeenEnhanced: true,
  },
  metrics: {
    processingTime: enhancementResult.metrics.processingTime,
  },
});
```

**Update translate route response**:
```typescript
// src/app/api/articles/translate/route.ts
return NextResponse.json({
  success: true,
  message: 'Article translated successfully',
  data: {
    germanTab: {
      germanTitle: translatedTitle,
      germanSummary: translatedSummary,
      germanContent: translatedContentLexical,
      germanKeyInsights: translatedKeyInsights,
      germanKeywords: translatedKeywords,
    },
    slug: generateGermanSlug(translatedTitle), // CONFIRMED: Replace slug completely
    workflowStage: 'translated',
    hasGermanTranslation: true,
  },
  metrics: {
    processingTime: translationResult.metrics.processingTime,
  },
});
```

### Task 3.2: Fix Form State Updates (45 minutes)
**File**: `src/components/admin/article-actions/DocumentControls.tsx`

**Problem**: Current implementation uses router.refresh() which causes jarring page reloads.

**Solution**: Proper form state updates using dispatchFields:

```typescript
// Update form after enhancement
const updateFormAfterEnhancement = useCallback((responseData: StandardAPIResponse) => {
  const updates = [
    // English tab updates
    {
      path: 'englishTab.enhancedTitle',
      value: responseData.data.englishTab?.enhancedTitle,
    },
    {
      path: 'englishTab.enhancedSummary', 
      value: responseData.data.englishTab?.enhancedSummary,
    },
    {
      path: 'englishTab.enhancedContent',
      value: responseData.data.englishTab?.enhancedContent,
    },
    {
      path: 'englishTab.keyInsights',
      value: responseData.data.englishTab?.keyInsights,
    },
    {
      path: 'englishTab.keywords',
      value: responseData.data.englishTab?.keywords,
    },
    // Main field updates
    {
      path: 'title',
      value: responseData.data.title,
    },
    {
      path: 'slug',
      value: responseData.data.slug,
    },
    {
      path: 'workflowStage',
      value: responseData.data.workflowStage,
    },
    {
      path: 'hasBeenEnhanced',
      value: responseData.data.hasBeenEnhanced,
    },
  ];

  // Apply all updates in batch
  updates.forEach(update => {
    if (update.value !== undefined) {
      dispatchFields({
        type: 'UPDATE',
        path: update.path,
        value: update.value,
      });
    }
  });

  // Force form validation refresh
  dispatchFields({ type: 'VALIDATE' });
}, [dispatchFields]);

// Update translation form state
const updateFormAfterTranslation = useCallback((responseData: StandardAPIResponse) => {
  const updates = [
    // German tab updates
    {
      path: 'germanTab.germanTitle',
      value: responseData.data.germanTab?.germanTitle,
    },
    {
      path: 'germanTab.germanSummary',
      value: responseData.data.germanTab?.germanSummary,
    },
    {
      path: 'germanTab.germanContent',
      value: responseData.data.germanTab?.germanContent,
    },
    {
      path: 'germanTab.germanKeyInsights',
      value: responseData.data.germanTab?.germanKeyInsights,
    },
    {
      path: 'germanTab.germanKeywords',
      value: responseData.data.germanTab?.germanKeywords,
    },
    // Main field updates
    {
      path: 'slug',
      value: responseData.data.slug, // CONFIRMED: Replace slug completely
    },
    {
      path: 'workflowStage',
      value: responseData.data.workflowStage,
    },
    {
      path: 'hasGermanTranslation',
      value: responseData.data.hasGermanTranslation,
    },
  ];

  // Apply all updates
  updates.forEach(update => {
    if (update.value !== undefined) {
      dispatchFields({
        type: 'UPDATE',
        path: update.path,
        value: update.value,
      });
    }
  });

  dispatchFields({ type: 'VALIDATE' });
}, [dispatchFields]);
```

### Task 3.3: Update Operation Handlers (30 minutes)

```typescript
// Update enhancement handler to use new response format
const handleEnhance = useCallback(async () => {
  if (!canEnhanceContent) return;
  
  setIsEnhancing(true);
  
  try {
    const response = await fetch('/api/articles/enhance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ articleId: id }),
    });

    const result: StandardAPIResponse = await response.json();

    if (result.success) {
      // Update form state immediately (no router.refresh!)
      updateFormAfterEnhancement(result);
      
      // Show success notification
      showNotification({
        type: 'success',
        message: result.message,
        duration: 3000,
      });
      
      // Set completion flag for UI feedback
      setEnhancementJustCompleted(true);
      setTimeout(() => setEnhancementJustCompleted(false), 2000);
    } else {
      showNotification({
        type: 'error',
        message: 'Enhancement failed',
        description: result.error,
      });
    }
  } catch (error) {
    showNotification({
      type: 'error',
      message: 'Enhancement failed',
      description: 'Failed to communicate with enhancement service',
    });
  } finally {
    setIsEnhancing(false);
  }
}, [canEnhanceContent, id, updateFormAfterEnhancement]);

// Similar update for handleTranslate...
```

### Task 3.4: Browser Tools API Testing (30 minutes)

**Testing Process**:
1. Use browser tools to monitor network requests
2. Test API responses match expected format
3. Monitor form state updates in real-time
4. Verify workflow stage transitions

**Console Debugging**:
```typescript
// Add API response debugging
console.log('🔍 API Response Debug:', {
  operation: 'enhance', // or 'translate'
  response: result,
  formUpdates: updates,
  newWorkflowStage: result.data.workflowStage,
});
```

### Task 3.5: Unit Tests (45 minutes)
**File**: `src/app/api/articles/__tests__/api-response-format.test.ts`

Test scenarios:
- [ ] API response format consistency
- [ ] Field mapping between API and UI
- [ ] Error response formats
- [ ] Workflow stage transitions
- [ ] Form state update logic

## 🧪 Testing Checklist

- [ ] API responses match StandardAPIResponse format
- [ ] Form state updates without page refresh
- [ ] Workflow stages transition correctly
- [ ] Slug updates work (enhance updates title/slug, translate replaces slug)
- [ ] Error handling works for API failures
- [ ] Network requests visible in browser tools

## 📊 Success Criteria

1. **Consistent API Format**: Both enhance and translate APIs return standardized responses
2. **Form State Updates**: Fields update immediately without page refresh
3. **Workflow Transitions**: Stages update correctly after operations
4. **Slug Handling**: Title/slug updates work as specified
5. **Error Handling**: Proper error responses and user feedback

## 🔄 Next Sprint

**Sprint 4**: User Experience & State Management - Improve loading states, notifications, and overall user experience.

## 📝 Notes

- Focus on eliminating router.refresh() completely
- Test API responses thoroughly with browser tools
- Ensure workflow stage names match confirmed requirements
- Document any API response format discoveries
