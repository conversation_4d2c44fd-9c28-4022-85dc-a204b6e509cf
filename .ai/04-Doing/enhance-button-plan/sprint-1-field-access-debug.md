# Sprint 1: Field Access & Debugging

**Duration**: 2-3 hours  
**Priority**: Critical (Blocks all other sprints)  
**Dependencies**: None

## 🎯 Sprint Goals

1. **Fix field access patterns** using proven PublicationReadinessIndicator patterns
2. **Implement comprehensive debugging** with browser-tools MCP integration
3. **Verify button visibility logic** works correctly
4. **Establish baseline functionality** for enhance and translate buttons

## 📋 Tasks

### Task 1.1: Setup Development Environment (30 minutes)
- [ ] Use browser-tools MCP to navigate to PayloadCMS admin
- [ ] Open article edit page for testing
- [ ] Setup console monitoring for real-time debugging
- [ ] Test current DocumentControls component behavior

### Task 1.2: Fix Field Path Resolution (45 minutes)
**File**: `src/components/admin/article-actions/DocumentControls.tsx`

**Current Issue**: Field access patterns may be incorrect, causing undefined values

**Solution**: Use proven patterns from PublicationReadinessIndicator:

```typescript
// ✅ CORRECTED: Use proven dot notation pattern from working components
const enhancedTitle = useFormFields(
  ([fields]) => fields['englishTab.enhancedTitle']?.value as string
);
const enhancedSummary = useFormFields(
  ([fields]) => fields['englishTab.enhancedSummary']?.value as string
);
const enhancedContent = useFormFields(
  ([fields]) => fields['englishTab.enhancedContent']?.value
);

// ✅ Add proper fallbacks and type safety
const title = useFormFields(([fields]) => fields.title?.value as string) || '';
const articleType = useFormFields(([fields]) => fields.articleType?.value as string) || 'curated';
const workflowStage = useFormFields(([fields]) => fields.workflowStage?.value as string) || 'curated-draft';
const hasBeenEnhanced = useFormFields(([fields]) => fields.hasBeenEnhanced?.value as boolean) || false;

// German fields using proven pattern
const germanTitle = useFormFields(([fields]) => fields['germanTab.germanTitle']?.value as string);
const germanContent = useFormFields(([fields]) => fields['germanTab.germanContent']?.value);
```

### Task 1.3: Add Comprehensive Debug Logging (30 minutes)

```typescript
// Add comprehensive debug logging to identify the real issue:
// NOTE: Use browser-tools MCP to monitor these logs in real-time during development
React.useEffect(() => {
  console.log('🔍 Field Access Debug:', {
    // Raw field structure
    allFields: Object.keys(fields),
    
    // English tab fields
    englishTabExists: !!fields['englishTab.enhancedTitle'],
    enhancedTitle: enhancedTitle || 'MISSING',
    enhancedSummary: enhancedSummary || 'MISSING',
    enhancedContent: enhancedContent ? 'Present' : 'MISSING',
    
    // Basic fields
    title: title || 'MISSING',
    articleType: articleType || 'MISSING',
    workflowStage: workflowStage || 'MISSING',
    hasBeenEnhanced: hasBeenEnhanced ? 'true' : 'false',
    
    // German fields
    germanTitle: germanTitle || 'MISSING',
    germanContent: germanContent ? 'Present' : 'MISSING',
  });
  
  // Additional debug for button visibility (monitor with browser tools)
  console.log('🔍 Button Visibility Debug:', {
    showEnhanceButton: articleType === 'curated',
    showTranslateButton: true,
    canEnhance: enhancedTitle?.length >= 20 && enhancedSummary?.length >= 20 && !!enhancedContent,
    canTranslate: enhancedTitle?.length >= 20 && enhancedSummary?.length >= 20 && !!enhancedContent,
  });
}, [fields, enhancedTitle, enhancedSummary, enhancedContent, title, articleType, workflowStage, hasBeenEnhanced, germanTitle, germanContent]);
```

### Task 1.4: Browser Tools Testing Workflow (45 minutes)

**Testing Process**:
1. Use browser-tools MCP to navigate to PayloadCMS admin article edit page
2. Monitor console logs in real-time while testing field access patterns
3. Test different article types (generated vs curated) and observe console output
4. Debug button visibility issues by watching console logs during state changes

**Test Scenarios**:
- [ ] Generated article with enhanced fields populated
- [ ] New curated article with empty fields
- [ ] Curated article with content >20 characters
- [ ] Article type switching (generated → curated)

### Task 1.5: Verify Button Visibility Logic (30 minutes)

**Expected Behavior**:
- **Generated Articles**: No enhance button, translate button visible
- **Curated Articles**: Both buttons visible after save
- **Button States**: Disabled until validation passes, enabled when ready

**Validation**:
- [ ] Enhance button hidden for generated articles
- [ ] Translate button visible for all article types
- [ ] Buttons disabled when fields <20 characters
- [ ] Buttons enabled when validation passes

## 🧪 Testing Checklist

- [ ] Field access patterns work correctly
- [ ] Console debugging provides useful information
- [ ] Button visibility matches requirements
- [ ] No JavaScript errors in console
- [ ] All field values are properly accessed

## 📊 Success Criteria

1. **Field Access**: All fields are properly accessed without undefined errors
2. **Debug Logging**: Comprehensive console output helps identify issues
3. **Button Visibility**: Buttons appear/hide according to business rules
4. **No Regressions**: Existing functionality continues to work
5. **Browser Tools Integration**: Real-time debugging workflow established

## 🔄 Next Sprint

**Sprint 2**: Centralized Validation Service - Build validation logic using the working field access patterns established in this sprint.

## 📝 Notes

- This sprint establishes the foundation for all subsequent work
- Focus on getting field access patterns correct before moving to validation logic
- Use browser-tools extensively for real-time debugging and immediate feedback
- Document any discoveries about field structure or PayloadCMS behavior
