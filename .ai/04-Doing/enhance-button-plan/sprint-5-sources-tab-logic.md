# Sprint 5: Sources Tab Conditional Logic

**Duration**: 2-3 hours  
**Priority**: Medium  
**Dependencies**: Sprint 2 (Validation Service)

## 🎯 Sprint Goals

1. **Implement conditional Sources tab visibility** based on article origin
2. **Update Articles collection schema** to support `hasOriginalSource` field
3. **Add tab visibility logic** to validation service
4. **Consider field renaming** for better clarity
5. **Test article type switching** scenarios

## 📋 Tasks

### Task 5.1: Update Articles Collection Schema (45 minutes)
**File**: `src/collections/Articles.ts`

**Problem**: Need to track whether an article has original RSS source content.

**Solution**: Add `hasOriginalSource` field and update tab visibility:

```typescript
// Add new field to Articles collection
{
  name: 'hasOriginalSource',
  type: 'checkbox',
  label: 'Has Original Source',
  admin: {
    description: 'Indicates if this article originated from RSS feed and has source content',
    readOnly: true, // Set programmatically, not user-editable
    position: 'sidebar',
  },
  defaultValue: false,
},

// Update tab configuration with conditional visibility
tabs: [
  {
    label: 'Sources',
    description: 'Original RSS content and source information',
    fields: [
      // ... existing source fields
    ],
    admin: {
      condition: (data: any) => {
        // Show Sources tab for:
        // 1. Generated articles (always have original source)
        // 2. Curated articles that were converted from generated (hasOriginalSource = true)
        return data.articleType === 'generated' || 
               (data.articleType === 'curated' && data.hasOriginalSource === true);
      },
    },
  },
  {
    label: 'English Content', // Consider renaming from "Enhanced English"
    description: 'Primary article content in English',
    fields: [
      // ... existing enhanced fields
    ],
  },
  {
    label: 'German Content',
    description: 'Translated article content in German',
    fields: [
      // ... existing German fields
    ],
  },
  {
    label: 'SEO',
    description: 'Search engine optimization settings',
    fields: [
      // ... existing SEO fields
    ],
  },
],
```

### Task 5.2: Update Validation Service with Tab Logic (30 minutes)
**File**: `src/lib/services/article-validation.ts`

**Enhancement**: Add tab visibility function from Sprint 2:

```typescript
/**
 * Determines tab visibility based on article type and source history
 * CONFIRMED REQUIREMENTS: Sources tab only for generated articles or converted articles
 */
export function getTabVisibility(context: ArticleValidationContext): {
  showSourcesTab: boolean;
  showEnglishTab: boolean;
  showGermanTab: boolean;
  showSeoTab: boolean;
} {
  const { articleType, hasOriginalSource } = context;

  return {
    // Sources tab: Only for generated articles or curated articles converted from generated
    showSourcesTab:
      articleType === 'generated' ||
      (articleType === 'curated' && hasOriginalSource),

    // English tab: Always show (primary content tab)
    showEnglishTab: true,

    // German tab: Always show (for translations)
    showGermanTab: true,

    // SEO tab: Always show (for meta information)
    showSeoTab: true,
  };
}

// Update ArticleValidationContext to ensure hasOriginalSource is included
export interface ArticleValidationContext {
  articleType: 'generated' | 'curated';
  workflowStage: string;
  hasBeenEnhanced: boolean;
  hasGermanTranslation: boolean;
  hasOriginalSource: boolean; // ✅ Already included from Sprint 2
  fields: {
    // ... existing fields
  };
}
```

### Task 5.3: Update DocumentControls to Use Tab Logic (30 minutes)
**File**: `src/components/admin/article-actions/DocumentControls.tsx`

**Enhancement**: Include `hasOriginalSource` in article context:

```typescript
// Update article context to include hasOriginalSource
const hasOriginalSource = useFormFields(([fields]) => fields.hasOriginalSource?.value as boolean) || false;

const articleContext = useMemo(() => ({
  articleType,
  workflowStage,
  hasBeenEnhanced,
  hasGermanTranslation: !!germanTitle || !!germanContent,
  hasOriginalSource, // ✅ Now included
  fields: {
    title,
    enhancedTitle,
    enhancedSummary,
    enhancedContent,
    germanTitle,
    germanContent,
  },
}), [articleType, workflowStage, hasBeenEnhanced, title, enhancedTitle, enhancedSummary, enhancedContent, germanTitle, germanContent, hasOriginalSource]);

// Add tab visibility debugging
const tabVisibility = getTabVisibility(articleContext);

console.log('🔍 Tab Visibility Debug:', {
  articleType,
  hasOriginalSource,
  tabVisibility,
});
```

### Task 5.4: Handle Article Type Switching (45 minutes)
**File**: `src/collections/Articles.ts`

**Problem**: When user switches from generated to curated, need to preserve source content access.

**Solution**: Add hooks to handle article type changes:

```typescript
// Add beforeChange hook to handle article type switching
hooks: {
  beforeChange: [
    async ({ data, operation, originalDoc, req }) => {
      // Handle article type switching
      if (operation === 'update' && originalDoc) {
        const wasGenerated = originalDoc.articleType === 'generated';
        const nowCurated = data.articleType === 'curated';
        
        // If switching from generated to curated, preserve hasOriginalSource
        if (wasGenerated && nowCurated) {
          data.hasOriginalSource = true;
          
          console.log('🔄 Article Type Switch:', {
            from: 'generated',
            to: 'curated',
            preservingSourceAccess: true,
            articleId: originalDoc.id,
          });
        }
        
        // If creating new curated article, ensure hasOriginalSource is false
        if (data.articleType === 'curated' && !originalDoc.hasOriginalSource) {
          data.hasOriginalSource = false;
        }
        
        // Generated articles always have original source
        if (data.articleType === 'generated') {
          data.hasOriginalSource = true;
        }
      }
      
      // For new articles
      if (operation === 'create') {
        data.hasOriginalSource = data.articleType === 'generated';
      }
      
      return data;
    },
  ],
},
```

### Task 5.5: Consider Field Renaming (30 minutes)
**Analysis**: Evaluate whether to rename "enhanced" fields to more generic names.

**Current Structure**:
- `englishTab.enhancedTitle` → Consider `englishTab.title` or `contentTab.title`
- `englishTab.enhancedSummary` → Consider `englishTab.summary` or `contentTab.summary`
- `englishTab.enhancedContent` → Consider `englishTab.content` or `contentTab.content`

**Decision Matrix**:
| Option | Pros | Cons | Impact |
|--------|------|------|--------|
| Keep "enhanced" | No migration needed | Confusing for curated articles | Low |
| Rename to "content" | More generic, clearer | Requires migration | High |
| Rename tab to "Content" | Clearer purpose | Minimal change needed | Medium |

**Recommendation**: Rename tab from "Enhanced English" to "English Content" (already done in schema update above).

### Task 5.6: Browser Tools Testing (30 minutes)

**Testing Scenarios**:
1. **New Generated Article**: Sources tab visible, hasOriginalSource = true
2. **New Curated Article**: Sources tab hidden, hasOriginalSource = false  
3. **Generated → Curated Switch**: Sources tab remains visible, hasOriginalSource = true
4. **Curated → Generated Switch**: Sources tab becomes visible, hasOriginalSource = true

**Console Debugging**:
```typescript
// Add comprehensive tab visibility debugging
console.log('🔍 Sources Tab Logic Debug:', {
  articleType,
  hasOriginalSource,
  showSourcesTab: tabVisibility.showSourcesTab,
  tabConditionMet: articleType === 'generated' || (articleType === 'curated' && hasOriginalSource),
});
```

### Task 5.7: Unit Tests (45 minutes)
**File**: `src/lib/services/__tests__/tab-visibility.test.ts`

Test scenarios:
- [ ] Generated article shows Sources tab
- [ ] New curated article hides Sources tab
- [ ] Generated→Curated conversion preserves Sources tab
- [ ] hasOriginalSource field updates correctly
- [ ] Tab visibility logic matches business rules

## 🧪 Testing Checklist

- [ ] Sources tab hidden for new curated articles
- [ ] Sources tab visible for generated articles
- [ ] Sources tab preserved when switching generated→curated
- [ ] hasOriginalSource field updates correctly
- [ ] Tab visibility logic works in PayloadCMS admin
- [ ] Article type switching preserves data correctly

## 📊 Success Criteria

1. **Conditional Tab Visibility**: Sources tab shows/hides based on article origin
2. **Schema Updates**: hasOriginalSource field tracks article source history
3. **Type Switching**: Generated→Curated preserves source access
4. **Data Integrity**: No data loss during article type changes
5. **User Experience**: Clear, logical tab organization

## 🔄 Next Sprint

**Sprint 6**: Comprehensive Testing & Validation - Create full test suite and validate all requirements.

## 📝 Notes

- Focus on data integrity during article type switching
- Test tab visibility thoroughly in PayloadCMS admin interface
- Consider user experience when tabs appear/disappear
- Document any edge cases discovered during testing
- Keep field renaming as optional enhancement for future consideration
