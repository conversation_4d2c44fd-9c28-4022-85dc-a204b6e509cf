# Enhance and Translate Button Functionality - Comprehensive Analysis and Fix Plan

## 🔍 Key Insights from Working Components

### Successful Field Access Patterns (from PublicationReadinessIndicator)

The **PublicationReadinessIndicator** component successfully accesses nested fields using these proven patterns:

```typescript
// ✅ WORKING: Direct nested field access with dot notation
const germanTitle = useFormFields(
  ([fields]) => fields['germanTab.germanTitle']?.value
);
const germanSummary = useFormFields(
  ([fields]) => fields['germanTab.germanSummary']?.value
);
const metaTitle = useFormFields(([fields]) => fields['meta.title']?.value);

// ✅ WORKING: Simple field access
const featuredImage = useFormFields(([fields]) => fields.featuredImage?.value);
const status = useFormFields(([fields]) => fields['_status']?.value);
```

**Key Learning**: The dot notation approach (`'germanTab.germanTitle'`) IS working in the existing codebase, so our DocumentControls field access issues are likely due to different problems.

### Successful Validation Patterns (from article-validation.ts)

The existing validation system shows these working patterns:

```typescript
// ✅ WORKING: Centralized validation functions
export function validateCategories(data: any): ArticleValidationResult {
  const errors: string[] = [];
  if (!data.categories || data.categories.length === 0) {
    errors.push('At least one category is required for publication.');
  }
  return { isValid: errors.length === 0, errors, warnings: [] };
}

// ✅ WORKING: Collection-level validation integration
hooks: {
  beforeChange: [
    async ({ data, operation, req, originalDoc }) => {
      const validationResult = validateArticleForPublication({
        data,
        operation,
        req,
        originalDoc,
      });
      if (!validationResult.isValid) {
        throw formatValidationError(validationResult);
      }
    },
  ];
}
```

## 🧪 Testing Strategy Clarification

### Current Testing Setup

- **Framework**: Vitest (already configured)
- **Location**: Tests in `__tests__` directories alongside source files
- **Commands**:
  - `pnpm test` - Run all tests
  - `pnpm test:watch` - Run tests in watch mode
  - `pnpm test:coverage` - Run with coverage report
- **Configuration**: `vitest.config.ts` with jsdom environment for React components

### Test Structure We'll Create

```
src/
├── components/admin/article-actions/
│   ├── DocumentControls.tsx
│   └── __tests__/
│       ├── DocumentControls.test.tsx          # Component integration tests
│       ├── field-access.test.tsx              # Field access pattern tests
│       └── button-states.test.tsx             # Button visibility/state tests
├── lib/services/
│   ├── article-validation.ts
│   ├── article-operations.ts
│   └── __tests__/
│       ├── article-validation.test.ts         # Validation logic tests
│       ├── article-operations.test.ts         # API communication tests
│       └── article-state-manager.test.ts      # State management tests
└── app/api/articles/
    ├── enhance/route.ts
    ├── translate/route.ts
    └── __tests__/
        ├── enhance-api.test.ts                # API endpoint tests
        ├── translate-api.test.ts              # API endpoint tests
        └── api-response-format.test.ts        # Response format tests
```

### Test Types We'll Implement

1. **Unit Tests**: Individual functions and components in isolation
2. **Integration Tests**: Component + API interactions
3. **Mock Tests**: PayloadCMS hooks and external API calls
4. **Regression Tests**: Specific broken use cases to prevent future breaks

### Example Test Files We'll Create

#### Component Tests

```typescript
// src/components/admin/article-actions/__tests__/DocumentControls.test.tsx
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ArticleDocumentControls } from '../DocumentControls';

describe('DocumentControls', () => {
  it('should show enhance button for curated articles', () => {
    // Mock PayloadCMS hooks with curated article data
    const mockFields = {
      articleType: { value: 'curated' },
      title: { value: 'Test Article Title' },
      workflowStage: { value: 'curated-draft' },
    };

    render(<ArticleDocumentControls />);
    expect(screen.getByText('Enhance Content')).toBeInTheDocument();
  });

  it('should disable enhance button when validation fails', () => {
    // Test validation logic
  });

  it('should call enhance API when button clicked', async () => {
    // Test API integration
  });
});
```

#### Validation Tests

```typescript
// src/lib/services/__tests__/article-validation.test.ts
import { describe, it, expect } from 'vitest';
import {
  validateForEnhancement,
  validateForTranslation,
} from '../article-validation';

describe('Article Validation', () => {
  describe('validateForEnhancement', () => {
    it('should pass validation for valid curated article', () => {
      const context = {
        articleType: 'curated' as const,
        workflowStage: 'curated-draft',
        hasBeenEnhanced: false,
        fields: {
          title: 'Valid title with enough characters',
        },
      };

      const result = validateForEnhancement(context);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should fail validation for generated article', () => {
      const context = {
        articleType: 'generated' as const,
        workflowStage: 'candidate-article',
        hasBeenEnhanced: false,
        fields: { title: 'Test' },
      };

      const result = validateForEnhancement(context);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Only curated articles can be enhanced');
    });
  });
});
```

#### API Tests

```typescript
// src/app/api/articles/__tests__/enhance-api.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { POST } from '../enhance/route';

describe('/api/articles/enhance', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should enhance article successfully', async () => {
    // Mock PayloadCMS and OpenAI
    const mockPayload = {
      findByID: vi.fn().mockResolvedValue({
        id: 'test-id',
        articleType: 'curated',
        workflowStage: 'curated-draft',
        title: 'Test Article',
      }),
      update: vi.fn().mockResolvedValue({}),
    };

    const request = new Request('http://localhost:3000/api/articles/enhance', {
      method: 'POST',
      body: JSON.stringify({ articleId: 'test-id' }),
    });

    const response = await POST(request);
    const result = await response.json();

    expect(result.success).toBe(true);
    expect(result.data.englishTab.enhancedTitle).toBeDefined();
  });

  it('should return error for invalid article type', async () => {
    // Test error cases
  });
});
```

### Running Tests

All tests run with a single command:

```bash
# Run all tests
pnpm test

# Run specific test suites
pnpm test DocumentControls
pnpm test article-validation
pnpm test enhance-api

# Run with coverage
pnpm test:coverage

# Watch mode for development
pnpm test:watch
```

## 📋 Complete Requirements Table

| **Scenario**                    | **Article Type**     | **Initial State**                                            | **Enhance Button**           | **Translate Button**     | **Validation Rules**      | **Success Behavior**                                   | **Button State After** |
| ------------------------------- | -------------------- | ------------------------------------------------------------ | ---------------------------- | ------------------------ | ------------------------- | ------------------------------------------------------ | ---------------------- |
| **Generated Article (Default)** | Generated (from RSS) | Enhanced fields populated, Editorial state: "Enhanced Draft" | ❌ Hidden (already enhanced) | ✅ Shown & Active        | Enhanced fields >20 chars | Toast + Auto-load German content                       | "Re-Translate"         |
| **Generated → Curated Switch**  | Curated (switched)   | Same content, different logic                                | ✅ Shown & Active            | ✅ Shown & Active        | All fields >20 chars      | Per curated workflow                                   | Per curated workflow   |
| **New Curated Article**         | Curated (manual)     | Empty/minimal content                                        | ✅ Shown but Disabled        | ✅ Shown but Disabled    | All fields >20 chars      | N/A until validation passes                            | N/A                    |
| **Curated - Valid Content**     | Curated (manual)     | All fields >20 chars                                         | ✅ Shown & Active            | ✅ Shown & Active        | All fields >20 chars      | Both operations available                              | Per operation          |
| **Curated - Enhance First**     | Curated (manual)     | User clicks Enhance                                          | ✅ Active → Loading          | ✅ Disabled during op    | Source fields >20 chars   | Toast + Auto-load enhanced content + Update slug/title | "Re-Enhance"           |
| **Curated - Translate First**   | Curated (manual)     | User clicks Translate                                        | ✅ Disabled during op        | ✅ Active → Loading      | Enhanced fields >20 chars | Toast + Auto-load German content                       | "Re-Translate"         |
| **Re-Enhancement**              | Curated (manual)     | After enhancement                                            | ✅ "Re-Enhance" Active       | ✅ Available             | Source fields >20 chars   | Toast + Overwrite enhanced content                     | "Re-Enhance"           |
| **Re-Translation**              | Any                  | After translation                                            | ✅ Per type logic            | ✅ "Re-Translate" Active | Enhanced fields >20 chars | Toast + Overwrite German content                       | "Re-Translate"         |

### Key Requirements Summary

#### **Generated Articles Workflow**

- ✅ **Default State**: Enhanced fields pre-populated, Editorial stage = "Enhanced Draft"
- ❌ **No Enhance Button**: Already enhanced by RSS pipeline
- ✅ **Translate Available**: If enhanced fields >20 characters
- 🔄 **Re-Translation**: Button changes to "Re-Translate" after first translation
- 🔀 **Type Switch**: If changed to Curated, show both buttons per curated logic

#### **Curated Articles Workflow**

- ✅ **Both Buttons Available**: User can enhance OR translate first (no required order)
- 🔒 **Validation Required**: All relevant fields must have >20 characters
- 🎯 **Enhance Operation**: Uses source fields → enhances → updates enhanced fields + slug + title
- 🌍 **Translate Operation**: Uses enhanced fields → translates → populates German fields
- 🔄 **Re-Operations**: Both enhance and translate can be repeated unlimited times

#### **Universal Rules**

- 💾 **Save First**: Article must be saved to database before operations
- 🚫 **Mutual Exclusion**: Cannot run enhance and translate simultaneously
- 📱 **User Feedback**: Toast notifications for all operations
- 🔄 **Auto-Refresh**: Page auto-loads new content after successful operations
- 🎯 **Field Sync**: English and German content stay synchronized through user-controlled operations

## ✅ Requirements Clarifications (CONFIRMED)

- **Editorial Stage Rename**: ✅ **CONFIRMED** - Rename "Enhanced English (Candidate)" to "Enhanced Draft" (will delete all articles to avoid migration errors)
- **Slug Translation**: ✅ **CONFIRMED** - German slug completely replaces English slug (single slug field)
- **Content Source Priority**: ✅ **CONFIRMED** - For curated articles, enhance uses "enhanced" fields as input (need to rename for clarity)
- **Sources Tab Logic**: ✅ **NEW REQUIREMENT** - Sources tab only for generated articles:
  - **Generated Articles**: Sources tab visible (contains original RSS content)
  - **Curated Articles (new)**: No Sources tab needed
  - **Generated → Curated (switched)**: Sources tab remains (tied to original article)

## Updated Field Structure Requirements

### Enhanced Fields Rename (More Generic)

Since curated articles use "enhanced" fields as their primary content fields, we should rename them:

- `englishTab.enhancedTitle` → `englishTab.title` or `contentTab.title`
- `englishTab.enhancedSummary` → `englishTab.summary` or `contentTab.summary`
- `englishTab.enhancedContent` → `englishTab.content` or `contentTab.content`

### Conditional Tab Visibility Logic

```typescript
// Sources tab visibility logic
const showSourcesTab = (articleType: string, hasOriginalSource: boolean) => {
  // Show for generated articles (always have original source)
  if (articleType === 'generated') return true;

  // Show for curated articles that were converted from generated
  if (articleType === 'curated' && hasOriginalSource) return true;

  // Hide for pure curated articles (created as curated from start)
  return false;
};
```

## Open Questions

- **Error Recovery Strategy**: What should happen when operations fail partway through (e.g., API succeeds but form update fails)?
- **Field Rename Strategy**: Should we rename "enhanced" fields to more generic names like "content" fields, or keep current naming?

## Phase 1: Critical Field Access and Validation Fixes

### Affected Files

- `src/components/admin/article-actions/DocumentControls.tsx` - Fix field access patterns and validation logic
- `src/lib/services/article-validation.ts` - Create centralized validation service (new file)
- `src/app/api/articles/enhance/route.ts` - Standardize API response format
- `src/app/api/articles/translate/route.ts` - Standardize API response format

### Summary of Changes

- Fix field path resolution for nested PayloadCMS tab structure
- Create centralized validation functions with consistent rules
- Standardize API response formats to match UI expectations
- Add comprehensive debug logging for troubleshooting

### Implementation Steps

#### Step 1: Fix Field Path Resolution (45 minutes)

**Problem**: DocumentControls component may have incorrect field paths or missing fallbacks, causing undefined values and broken validation.

**File**: `src/components/admin/article-actions/DocumentControls.tsx`

Based on the working PublicationReadinessIndicator patterns, let's use the proven approach:

```typescript
// ✅ CORRECTED: Use proven dot notation pattern from working components
const enhancedTitle = useFormFields(
  ([fields]) => fields['englishTab.enhancedTitle']?.value as string
);
const enhancedSummary = useFormFields(
  ([fields]) => fields['englishTab.enhancedSummary']?.value as string
);
const enhancedContent = useFormFields(
  ([fields]) => fields['englishTab.enhancedContent']?.value
);

// ✅ Add proper fallbacks and type safety
const title = useFormFields(([fields]) => fields.title?.value as string) || '';
const articleType =
  useFormFields(([fields]) => fields.articleType?.value as string) || 'curated';
const workflowStage =
  useFormFields(([fields]) => fields.workflowStage?.value as string) ||
  'curated-draft';
const hasBeenEnhanced =
  useFormFields(([fields]) => fields.hasBeenEnhanced?.value as boolean) ||
  false;

// German fields using proven pattern
const germanTitle = useFormFields(
  ([fields]) => fields['germanTab.germanTitle']?.value as string
);
const germanContent = useFormFields(
  ([fields]) => fields['germanTab.germanContent']?.value
);

// Add comprehensive debug logging to identify the real issue:
React.useEffect(() => {
  console.log('🔍 Field Access Debug:', {
    // Raw field structure
    allFields: Object.keys(fields),

    // English tab fields
    englishTabExists: !!fields['englishTab.enhancedTitle'],
    enhancedTitle: enhancedTitle || 'MISSING',
    enhancedSummary: enhancedSummary || 'MISSING',
    enhancedContent: enhancedContent ? 'Present' : 'MISSING',

    // Basic fields
    title: title || 'MISSING',
    articleType: articleType || 'MISSING',
    workflowStage: workflowStage || 'MISSING',
    hasBeenEnhanced: hasBeenEnhanced ? 'true' : 'false',

    // German fields
    germanTitle: germanTitle || 'MISSING',
    germanContent: germanContent ? 'Present' : 'MISSING',
  });
}, [
  fields,
  enhancedTitle,
  enhancedSummary,
  enhancedContent,
  title,
  articleType,
  workflowStage,
  hasBeenEnhanced,
  germanTitle,
  germanContent,
]);
```

**Browser Tools Development Workflow**:

1. Use browser-tools MCP to navigate to PayloadCMS admin article edit page
2. Monitor console logs in real-time while testing field access patterns
3. Test different article types (generated vs curated) and observe console output
4. Debug button visibility issues by watching console logs during state changes

**Unit Tests**: Create `src/components/admin/article-actions/__tests__/field-access.test.tsx`

- Test field access with different article states
- Test fallback handling for undefined fields
- Test field access with generated vs curated articles

#### Step 2: Create Centralized Validation Service (60 minutes)

**Problem**: Validation logic is scattered across multiple files with inconsistent rules and requirements.

**File**: `src/lib/services/article-validation.ts` (new file)

```typescript
export interface ArticleValidationContext {
  articleType: 'generated' | 'curated';
  workflowStage: string;
  hasBeenEnhanced: boolean;
  hasGermanTranslation: boolean;
  hasOriginalSource: boolean; // NEW: Track if article has original RSS source
  fields: {
    // Main article fields
    title?: string;

    // Enhanced/Content fields (primary content for both types)
    // NOTE: These should be renamed to be more generic (e.g., contentTitle, contentSummary)
    enhancedTitle?: string;
    enhancedSummary?: string;
    enhancedContent?: any;

    // Source fields (ONLY for generated articles or converted articles)
    originalTitle?: string;
    originalContent?: any;
    originalSummary?: string;

    // German fields (for re-translation detection)
    germanTitle?: string;
    germanContent?: any;
  };
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  buttonText?: string;
}

/**
 * Validates if article can be enhanced
 * NEW REQUIREMENT: Only curated articles can be enhanced, using enhanced fields as input
 */
export function validateForEnhancement(
  context: ArticleValidationContext
): ValidationResult {
  const { articleType, fields, hasBeenEnhanced } = context;
  const errors: string[] = [];

  // Generated articles cannot be enhanced (already enhanced by RSS pipeline)
  if (articleType === 'generated') {
    return {
      isValid: false,
      errors: [
        'Generated articles are already enhanced and cannot be re-enhanced',
      ],
      warnings: [],
    };
  }

  // For curated articles, validate enhanced fields (20+ character requirement)
  // CONFIRMED: Curated articles use enhanced fields as input for enhancement
  if (!fields.enhancedTitle || fields.enhancedTitle.length < 20) {
    errors.push('Title must be at least 20 characters');
  }

  if (!fields.enhancedSummary || fields.enhancedSummary.length < 20) {
    errors.push('Summary must be at least 20 characters');
  }

  if (!fields.enhancedContent) {
    errors.push('Content is required for enhancement');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
    buttonText: hasBeenEnhanced ? 'Re-Enhance' : 'Enhance Content',
  };
}

/**
 * Validates if article can be translated
 * NEW REQUIREMENT: Both generated and curated can translate, different validation rules
 */
export function validateForTranslation(
  context: ArticleValidationContext
): ValidationResult {
  const { articleType, fields, hasGermanTranslation } = context;
  const errors: string[] = [];

  if (articleType === 'generated') {
    // Generated articles: validate enhanced fields (should be pre-populated)
    if (!fields.enhancedTitle || fields.enhancedTitle.length < 20) {
      errors.push('Enhanced title must be at least 20 characters');
    }
    if (!fields.enhancedSummary || fields.enhancedSummary.length < 20) {
      errors.push('Enhanced summary must be at least 20 characters');
    }
    if (!fields.enhancedContent) {
      errors.push('Enhanced content is required');
    }
  } else if (articleType === 'curated') {
    // Curated articles: validate enhanced fields (user can translate without enhancing first)
    if (!fields.enhancedTitle || fields.enhancedTitle.length < 20) {
      errors.push('Enhanced title must be at least 20 characters');
    }
    if (!fields.enhancedSummary || fields.enhancedSummary.length < 20) {
      errors.push('Enhanced summary must be at least 20 characters');
    }
    if (!fields.enhancedContent) {
      errors.push('Enhanced content is required');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings: [],
    buttonText: hasGermanTranslation ? 'Re-Translate' : 'Translate to German',
  };
}

/**
 * Determines button visibility based on article type and state
 */
export function getButtonVisibility(context: ArticleValidationContext): {
  showEnhanceButton: boolean;
  showTranslateButton: boolean;
} {
  const { articleType } = context;

  return {
    // Enhance button: Only show for curated articles
    showEnhanceButton: articleType === 'curated',

    // Translate button: Show for all article types
    showTranslateButton: true,
  };
}

/**
 * Determines tab visibility based on article type and source history
 * NEW REQUIREMENT: Sources tab only for generated articles
 */
export function getTabVisibility(context: ArticleValidationContext): {
  showSourcesTab: boolean;
  showEnglishTab: boolean;
  showGermanTab: boolean;
  showSeoTab: boolean;
} {
  const { articleType, hasOriginalSource } = context;

  return {
    // Sources tab: Only for generated articles or curated articles converted from generated
    showSourcesTab:
      articleType === 'generated' ||
      (articleType === 'curated' && hasOriginalSource),

    // English tab: Always show (primary content tab)
    showEnglishTab: true,

    // German tab: Always show (for translations)
    showGermanTab: true,

    // SEO tab: Always show (for meta information)
    showSeoTab: true,
  };
}
```

**Unit Tests**: Create `src/lib/services/__tests__/article-validation.test.ts`

- Test validation for different article types and workflow stages
- Test edge cases with missing or invalid field data
- Test validation message accuracy and helpfulness

#### Step 3: Update DocumentControls to Use Centralized Validation (30 minutes)

**File**: `src/components/admin/article-actions/DocumentControls.tsx`

```typescript
import {
  validateForEnhancement,
  validateForTranslation,
} from '@/lib/services/article-validation';

// Replace existing validation functions with centralized service:
const enhanceValidation = validateForEnhancement({
  articleType,
  workflowStage,
  hasBeenEnhanced,
  fields: {
    title,
    enhancedTitle,
    enhancedSummary,
    enhancedContent,
  },
});

const translateValidation = validateForTranslation({
  articleType,
  workflowStage,
  hasBeenEnhanced,
  fields: {
    enhancedTitle,
    enhancedSummary,
    enhancedContent,
    germanTitle,
    germanContent,
  },
});

// Update button logic to use validation results:
const canEnhanceContent =
  !!id && enhanceValidation.isValid && !isEnhancing && !isTranslating;
const canTranslate =
  !!id && translateValidation.isValid && !isTranslating && !isEnhancing;

// Update validation message function:
const getValidationMessage = useCallback(() => {
  if (!id) return 'Please save the article first';
  if (!enhanceValidation.isValid) return enhanceValidation.errors[0];
  if (!translateValidation.isValid) return translateValidation.errors[0];
  return null;
}, [id, enhanceValidation, translateValidation]);
```

#### Step 4: Standardize API Response Formats (45 minutes)

**Problem**: API responses don't match the format expected by the UI component for form updates.

**Files**:

- `src/app/api/articles/enhance/route.ts`
- `src/app/api/articles/translate/route.ts`

```typescript
// Standardize response format for both APIs:
interface StandardAPIResponse {
  success: boolean;
  message: string;
  data: {
    // Fields that match PayloadCMS structure
    englishTab?: {
      enhancedTitle?: string;
      enhancedSummary?: string;
      enhancedContent?: any;
      keyInsights?: string[];
      keywords?: string[];
    };
    germanTab?: {
      germanTitle?: string;
      germanSummary?: string;
      germanContent?: any;
      germanKeyInsights?: string[];
      germanKeywords?: string[];
    };
    workflowStage?: string;
    hasBeenEnhanced?: boolean;
    hasGermanTranslation?: boolean;
  };
  metrics?: {
    processingTime: number;
    [key: string]: any;
  };
  error?: string;
}

// Update enhance route response:
return NextResponse.json({
  success: true,
  message: 'Article enhanced successfully',
  data: {
    englishTab: {
      enhancedTitle: cleanTitle,
      enhancedSummary: enhancedSummary,
      enhancedContent: enhancedContentLexical,
      keyInsights: keyInsights,
      keywords: enhancedData.enhancedContent.keywords,
    },
    workflowStage: 'candidate-article',
    hasBeenEnhanced: true,
  },
  metrics: {
    processingTime: enhancementResult.metrics.processingTime,
    // ... other metrics
  },
});
```

**Unit Tests**: Create `src/app/api/articles/__tests__/api-response-format.test.ts`

- Test API response format consistency
- Test field mapping between API and UI
- Test error response formats

## Phase 2: State Management and User Experience Improvements

### Affected Files

- `src/components/admin/article-actions/DocumentControls.tsx` - Improve form state updates and error handling
- `src/lib/services/article-operations.ts` - Extract business logic (new file)
- `src/components/admin/notifications/ArticleNotifications.tsx` - Replace temporary toast (new file)

### Summary of Changes

- Replace router.refresh() with proper form state management
- Implement PayloadCMS-native notification system
- Add proper loading states and user feedback
- Extract business logic from UI component

### Implementation Steps

#### Step 1: Improve Form State Management (60 minutes)

**Problem**: Current implementation uses router.refresh() which causes jarring page reloads and potential state loss.

**File**: `src/components/admin/article-actions/DocumentControls.tsx`

```typescript
// Replace router.refresh() approach with proper form state updates:
const updateFormAfterEnhancement = useCallback(
  (responseData: any) => {
    // Update multiple fields in a single batch
    const updates = [
      {
        path: 'englishTab.enhancedTitle',
        value: responseData.data.englishTab.enhancedTitle,
      },
      {
        path: 'englishTab.enhancedSummary',
        value: responseData.data.englishTab.enhancedSummary,
      },
      {
        path: 'englishTab.enhancedContent',
        value: responseData.data.englishTab.enhancedContent,
      },
      {
        path: 'workflowStage',
        value: responseData.data.workflowStage,
      },
      {
        path: 'hasBeenEnhanced',
        value: responseData.data.hasBeenEnhanced,
      },
    ];

    // Apply all updates
    updates.forEach(update => {
      dispatchFields({
        type: 'UPDATE',
        path: update.path,
        value: update.value,
      });
    });

    // Force form validation refresh
    dispatchFields({ type: 'VALIDATE' });
  },
  [dispatchFields]
);

// Update enhancement handler:
const handleEnhance = useCallback(async () => {
  if (!canEnhanceContent) return;

  setIsEnhancing(true);

  try {
    const response = await fetch('/api/articles/enhance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ articleId: id }),
    });

    const result = await response.json();

    if (result.success) {
      // Update form state immediately
      updateFormAfterEnhancement(result);

      // Show success notification
      showNotification({
        type: 'success',
        message: 'Content enhanced successfully!',
        duration: 3000,
      });

      // Set completion flag for UI feedback
      setEnhancementJustCompleted(true);
      setTimeout(() => setEnhancementJustCompleted(false), 2000);
    } else {
      showNotification({
        type: 'error',
        message: 'Enhancement failed',
        description: result.error,
      });
    }
  } catch (error) {
    showNotification({
      type: 'error',
      message: 'Enhancement failed',
      description: 'Failed to communicate with enhancement service',
    });
  } finally {
    setIsEnhancing(false);
  }
}, [canEnhanceContent, id, updateFormAfterEnhancement]);
```

#### Step 2: Implement PayloadCMS-Native Notifications (45 minutes)

**Problem**: Current toast implementation is temporary and doesn't integrate with PayloadCMS notification system.

**File**: `src/components/admin/notifications/ArticleNotifications.tsx` (new file)

```typescript
'use client';

import { useToast } from '@payloadcms/ui';

export interface NotificationOptions {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  description?: string;
  duration?: number;
}

export const useArticleNotifications = () => {
  const { toast } = useToast();

  const showNotification = useCallback(
    (options: NotificationOptions) => {
      const { type, message, description, duration = 5000 } = options;

      toast[type](message, {
        description,
        duration,
      });
    },
    [toast]
  );

  return { showNotification };
};
```

**File**: `src/components/admin/article-actions/DocumentControls.tsx`

```typescript
// Replace temporary toast with PayloadCMS notifications:
import { useArticleNotifications } from '../notifications/ArticleNotifications';

export const ArticleDocumentControls = () => {
  const { showNotification } = useArticleNotifications();

  // Use showNotification instead of temporary toast
  // ... rest of component
};
```

#### Step 3: Extract Business Logic (75 minutes)

**Problem**: DocumentControls component contains too much business logic, making it hard to test and maintain.

**File**: `src/lib/services/article-operations.ts` (new file)

```typescript
export class ArticleOperationsService {
  async enhanceArticle(articleId: string): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      const response = await fetch('/api/articles/enhance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to communicate with enhancement service',
      };
    }
  }

  async translateArticle(articleId: string): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      const response = await fetch('/api/articles/translate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ articleId }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to communicate with translation service',
      };
    }
  }
}

export const articleOperations = new ArticleOperationsService();
```

**Unit Tests**: Create `src/lib/services/__tests__/article-operations.test.ts`

- Test API communication and error handling
- Test response format validation
- Test retry mechanisms and timeout handling

## Phase 3: Architecture Improvements and Testing

### Affected Files

- `src/components/admin/article-actions/DocumentControls.tsx` - Final cleanup and optimization
- `src/lib/services/article-state-manager.ts` - Centralized state management (new file)
- `src/lib/hooks/useArticleOperations.ts` - Custom hook for operations (new file)
- Multiple test files for comprehensive coverage

### Summary of Changes

- Create centralized state management for article operations
- Implement custom hooks for better separation of concerns
- Add comprehensive error recovery mechanisms
- Implement full test coverage for all components

### Implementation Steps

#### Step 1: Create Centralized State Manager (90 minutes)

**File**: `src/lib/services/article-state-manager.ts` (new file)

```typescript
export interface ArticleState {
  isEnhancing: boolean;
  isTranslating: boolean;
  enhancementJustCompleted: boolean;
  translationJustCompleted: boolean;
  lastError: string | null;
  validationState: {
    canEnhance: boolean;
    canTranslate: boolean;
    errors: string[];
  };
}

export class ArticleStateManager {
  private state: ArticleState = {
    isEnhancing: false,
    isTranslating: false,
    enhancementJustCompleted: false,
    translationJustCompleted: false,
    lastError: null,
    validationState: {
      canEnhance: false,
      canTranslate: false,
      errors: [],
    },
  };

  private listeners: ((state: ArticleState) => void)[] = [];

  subscribe(listener: (state: ArticleState) => void) {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notify() {
    this.listeners.forEach(listener => listener({ ...this.state }));
  }

  updateValidation(validationState: ArticleState['validationState']) {
    this.state.validationState = validationState;
    this.notify();
  }

  setEnhancing(isEnhancing: boolean) {
    this.state.isEnhancing = isEnhancing;
    if (isEnhancing) {
      this.state.lastError = null;
    }
    this.notify();
  }

  setTranslating(isTranslating: boolean) {
    this.state.isTranslating = isTranslating;
    if (isTranslating) {
      this.state.lastError = null;
    }
    this.notify();
  }

  setEnhancementComplete() {
    this.state.isEnhancing = false;
    this.state.enhancementJustCompleted = true;
    setTimeout(() => {
      this.state.enhancementJustCompleted = false;
      this.notify();
    }, 2000);
    this.notify();
  }

  setTranslationComplete() {
    this.state.isTranslating = false;
    this.state.translationJustCompleted = true;
    setTimeout(() => {
      this.state.translationJustCompleted = false;
      this.notify();
    }, 2000);
    this.notify();
  }

  setError(error: string) {
    this.state.lastError = error;
    this.state.isEnhancing = false;
    this.state.isTranslating = false;
    this.notify();
  }

  getState(): ArticleState {
    return { ...this.state };
  }
}
```

#### Step 2: Create Custom Hook for Operations (60 minutes)

**File**: `src/lib/hooks/useArticleOperations.ts` (new file)

```typescript
import { useCallback, useEffect, useState } from 'react';
import { ArticleStateManager } from '../services/article-state-manager';
import { articleOperations } from '../services/article-operations';
import {
  validateForEnhancement,
  validateForTranslation,
} from '../services/article-validation';

export const useArticleOperations = (articleContext: {
  id: string;
  articleType: 'generated' | 'curated';
  workflowStage: string;
  hasBeenEnhanced: boolean;
  fields: any;
}) => {
  const [stateManager] = useState(() => new ArticleStateManager());
  const [state, setState] = useState(stateManager.getState());

  useEffect(() => {
    const unsubscribe = stateManager.subscribe(setState);
    return unsubscribe;
  }, [stateManager]);

  // Update validation when context changes
  useEffect(() => {
    const enhanceValidation = validateForEnhancement(articleContext);
    const translateValidation = validateForTranslation(articleContext);

    stateManager.updateValidation({
      canEnhance: enhanceValidation.isValid,
      canTranslate: translateValidation.isValid,
      errors: [...enhanceValidation.errors, ...translateValidation.errors],
    });
  }, [articleContext, stateManager]);

  const enhanceArticle = useCallback(async () => {
    if (!state.validationState.canEnhance) return;

    stateManager.setEnhancing(true);

    try {
      const result = await articleOperations.enhanceArticle(articleContext.id);

      if (result.success) {
        stateManager.setEnhancementComplete();
        return result;
      } else {
        stateManager.setError(result.error || 'Enhancement failed');
        return result;
      }
    } catch (error) {
      stateManager.setError('Enhancement failed');
      return { success: false, error: 'Enhancement failed' };
    }
  }, [articleContext.id, state.validationState.canEnhance, stateManager]);

  const translateArticle = useCallback(async () => {
    if (!state.validationState.canTranslate) return;

    stateManager.setTranslating(true);

    try {
      const result = await articleOperations.translateArticle(
        articleContext.id
      );

      if (result.success) {
        stateManager.setTranslationComplete();
        return result;
      } else {
        stateManager.setError(result.error || 'Translation failed');
        return result;
      }
    } catch (error) {
      stateManager.setError('Translation failed');
      return { success: false, error: 'Translation failed' };
    }
  }, [articleContext.id, state.validationState.canTranslate, stateManager]);

  return {
    state,
    enhanceArticle,
    translateArticle,
  };
};
```

#### Step 3: Refactor DocumentControls to Use New Architecture (45 minutes)

**File**: `src/components/admin/article-actions/DocumentControls.tsx`

```typescript
// Simplified component using new architecture:
export const ArticleDocumentControls = () => {
  const docInfo = useDocumentInfo();
  const { id } = docInfo;
  const [fields, dispatchFields] = useAllFormFields();

  // Extract field values (using corrected field access)
  const articleContext = useMemo(() => ({
    id: id || '',
    articleType: fields.articleType?.value || 'curated',
    workflowStage: fields.workflowStage?.value || 'curated-draft',
    hasBeenEnhanced: fields.hasBeenEnhanced?.value || false,
    fields: {
      title: fields.title?.value || '',
      enhancedTitle: fields.englishTab?.value?.enhancedTitle || '',
      enhancedSummary: fields.englishTab?.value?.enhancedSummary || '',
      enhancedContent: fields.englishTab?.value?.enhancedContent || null,
      germanTitle: fields.germanTab?.value?.germanTitle || '',
      germanContent: fields.germanTab?.value?.germanContent || null,
    },
  }), [id, fields]);

  const { state, enhanceArticle, translateArticle } = useArticleOperations(articleContext);
  const { showNotification } = useArticleNotifications();

  const handleEnhance = useCallback(async () => {
    const result = await enhanceArticle();

    if (result?.success) {
      // Update form fields
      updateFormAfterEnhancement(result);
      showNotification({
        type: 'success',
        message: 'Content enhanced successfully!',
      });
    } else {
      showNotification({
        type: 'error',
        message: 'Enhancement failed',
        description: result?.error,
      });
    }
  }, [enhanceArticle, showNotification]);

  const handleTranslate = useCallback(async () => {
    const result = await translateArticle();

    if (result?.success) {
      // Update form fields
      updateFormAfterTranslation(result);
      showNotification({
        type: 'success',
        message: 'Translation completed successfully!',
      });
    } else {
      showNotification({
        type: 'error',
        message: 'Translation failed',
        description: result?.error,
      });
    }
  }, [translateArticle, showNotification]);

  // Simplified render logic
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      {/* Enhancement Button */}
      {articleContext.articleType === 'curated' && id && (
        <button
          onClick={handleEnhance}
          disabled={!state.validationState.canEnhance || state.isEnhancing || state.isTranslating}
          style={{
            backgroundColor: state.isEnhancing ? '#6B7280' : '#3B82F6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            padding: '8px 16px',
            cursor: state.validationState.canEnhance ? 'pointer' : 'not-allowed',
          }}
        >
          {state.isEnhancing ? 'Enhancing...' : 'Enhance Content'}
        </button>
      )}

      {/* Translation Button */}
      {id && (
        <button
          onClick={handleTranslate}
          disabled={!state.validationState.canTranslate || state.isTranslating || state.isEnhancing}
          style={{
            backgroundColor: state.isTranslating ? '#6B7280' : '#10B981',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            padding: '8px 16px',
            cursor: state.validationState.canTranslate ? 'pointer' : 'not-allowed',
          }}
        >
          {state.isTranslating ? 'Translating...' : 'Translate to German'}
        </button>
      )}
    </div>
  );
};
```

#### Step 4: Comprehensive Testing Suite (120 minutes)

**Files**: Multiple test files for complete coverage

```typescript
// src/components/admin/article-actions/__tests__/DocumentControls.test.tsx
// src/lib/services/__tests__/article-validation.test.ts
// src/lib/services/__tests__/article-operations.test.ts
// src/lib/services/__tests__/article-state-manager.test.ts
// src/lib/hooks/__tests__/useArticleOperations.test.ts
```

Test coverage should include:

- Field access patterns with different article states
- Validation logic for all article types and workflow stages
- API communication and error handling
- State management and transitions
- User interaction flows
- Error recovery scenarios

## Task Checklist

### Phase 1: Critical Fixes (Based on New Requirements)

☐ **Setup Development Environment with Browser Tools**:

- Use browser-tools MCP to navigate to PayloadCMS admin
- Monitor console logs during development for real-time debugging
- Test field access patterns and validation logic live
  ☐ Fix field path resolution using proven patterns from PublicationReadinessIndicator
  ☐ Create centralized validation service with new business rules:
- Generated articles: No enhance button, translate button with enhanced field validation
- Curated articles: Both buttons available, 20+ character validation for all fields
- Button text changes: "Re-Enhance" and "Re-Translate" after first operation
  ☐ Update DocumentControls to use centralized validation and button visibility logic
  ☐ Standardize API response formats to match new field structure
  ☐ **Live Testing with Browser Console Monitoring**:
- Test all scenarios from requirements table using browser tools
- Monitor console logs for field access issues and validation errors
- Debug button visibility and state changes in real-time
  ☐ Verify button visibility and state changes work correctly

### Phase 2: User Experience (Enhanced Requirements)

☐ Implement proper form state management without router.refresh()
☐ Replace temporary toast with PayloadCMS native notifications
☐ Add comprehensive loading states during operations
☐ Implement mutual exclusion (prevent simultaneous enhance/translate)
☐ Add auto-refresh functionality to load new content after operations
☐ Test re-operation workflows (re-enhance, re-translate)
☐ Verify field synchronization between English and German content

### Phase 3: Architecture & Testing

☐ Create centralized state manager for complex operation states
☐ Implement custom hook for operations with proper error handling
☐ Refactor DocumentControls to use new architecture
☐ Create comprehensive test suite covering all scenarios:

- Generated article workflows
- Curated article workflows
- Article type switching
- Re-operation scenarios
- Error handling and recovery
  ☐ Performance testing and optimization
  ☐ Documentation and code review

### Phase 4: Requirements Validation

☐ Test Generated Article workflow:

- Enhance button hidden
- Translate button active when enhanced fields >20 chars
- Re-translate functionality works
- Article type switch to curated shows enhance button
  ☐ Test Curated Article workflow:
- Both buttons available after save
- User can enhance OR translate first (no required order)
- Re-enhance and re-translate work correctly
- Field synchronization maintained
  ☐ Test Universal Requirements:
  - Save-first requirement enforced
  - Mutual exclusion during operations
  - Toast notifications for all operations
  - Auto-refresh after successful operations
    ☐ Rename "Enhanced English (Candidate)" to "Enhanced Draft" in workflow stages
    ☐ Implement conditional Sources tab visibility:
  - Hide Sources tab for new curated articles
  - Show Sources tab for generated articles
  - Keep Sources tab for generated→curated converted articles
    ☐ Update Articles collection schema to support hasOriginalSource field
    ☐ Consider renaming "enhanced" fields to more generic "content" fields
